# WebApp Architecture - Next.js Application

## 🎯 Tổng quan

WebApp là ứng dụng web chính của Medoo Platform, đư<PERSON>c xây dựng với Next.js 14 và App Router. Đây là ứng dụng full-stack với SSR/SSG, hỗ trợ đa theme và đa ngôn ngữ.

## 📁 Cấu trúc Th<PERSON> mục

```
packages/webapp/
├── app/                    # App Router (Next.js 14)
│   ├── [locale]/          # Internationalization routing
│   │   ├── layout.tsx     # Root layout
│   │   ├── page.tsx       # Homepage
│   │   ├── users/         # User-related pages
│   │   ├── courses/       # Course pages
│   │   └── ...
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   └── i18n/              # i18n configuration
├── components/            # Reusable components
│   ├── features/          # Feature-specific components
│   ├── shared/            # Shared components
│   └── ui/                # UI components
├── layouts/               # Layout components
├── page-zones/            # Theme-specific page implementations
├── themes/                # Theme system
├── lib/                   # Utility libraries
├── store/                 # Redux store
├── styles/                # Styling files
├── public/                # Static assets
├── next.config.js         # Next.js configuration
├── tailwind.config.js     # Tailwind configuration
└── package.json
```

## ⚙️ Next.js Configuration

### next.config.js
```javascript
const nextConfigs = {
  // Compiler optimizations
  compiler: {
    styledComponents: false,
    reactRemoveProperties: true,
  },
  swcMinify: true,
  reactStrictMode: false,
  
  // Image optimization
  images: {
    unoptimized: true,
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    domains: ["medoo.online", "medoo.io", "localhost"],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**.cloudfront.net",
      },
      {
        protocol: "https", 
        hostname: "**.amazonaws.com",
      }
    ]
  },
  
  // Webpack customization
  webpack: (config, { webpack, isServer, dev }) => {
    config.plugins.push(
      new webpack.IgnorePlugin({ resourceRegExp: /^electron$/ })
    );
    config.resolve.fallback = { fs: false, net: false, tls: false };
    config.experiments = {
      asyncWebAssembly: true,
      layers: true,
    };
    return config;
  },
  
  // Performance optimizations
  experimental: {
    optimizePackageImports: [
      "@near-js",
      "@near-wallet-selector", 
      "connectkit",
    ],
  }
};
```

## 🎨 Theme System

### Theme Architecture
```typescript
// Theme registration
export const ThemeRegistered = {
  [THEME_NAMES.MEDOO_SAAS_V4]: MedooSaasV4,
  [THEME_NAMES.MEDOO_WEB3]: MedooWeb3Theme,
  [THEME_NAMES.EDUMALL_V2]: EdumallV2,
  // ... other themes
};

// Theme context
interface MeliThemeContextType {
  theme?: string;
  site?: any;
  onSiteChange?: any;
  pageName?: string;
  layoutProps?: Record<string, any>;
  styleTheme?: any;
  onChangeTheme?: any;
}
```

### Page-Zone Pattern
Mỗi theme có implementation riêng cho từng page:
```typescript
// home-page.paths.ts
export const HomepageRegistered = {
  [THEME_NAMES.MEDOO]: Web3Home,
  [THEME_NAMES.EDUMALL_V2]: EdumallV2HomePage,
  [THEME_NAMES.MEDOO_SAAS_V4]: MedooSaasV4HomePage,
  [THEME_NAMES.MEDOO_DIGITAL_UNIVERSITY]: MedooDigitalUniversityHomePage,
};

// signup-page.paths.ts
export const SingupPagePaths = {
  [THEME_NAMES.EDUMALL_V2]: {
    component: EdumallV2Signup,
    layout: EdumallV2AuthenLayout,
  },
  [THEME_NAMES.MEDOO_SAAS_WEB3]: {
    component: SignupUserFormWeb3Saas,
    layout: AuthenticationUserWeb3SaasLayout,
  },
};
```

## 🗂️ App Router Structure

### Layout Hierarchy
```typescript
// app/[locale]/layout.tsx
export default function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  return (
    <html lang={locale === Languages.VN ? "vi" : locale}>
      <body>
        <div id="mainViewRef" className="height100percent ui-h2platform-system">
          <StoreProvider>
            <MeliThemeProvider>
              <AuthProvider>
                <CurrencyProvider>
                  <MeliPortalContextProvider>
                    <GoogleOAuthProvider>
                      <TopProgressBar />
                      {children}
                    </GoogleOAuthProvider>
                  </MeliPortalContextProvider>
                </CurrencyProvider>
              </AuthProvider>
            </MeliThemeProvider>
          </StoreProvider>
        </div>
      </body>
    </html>
  );
}
```

### Dynamic Page Resolution
```typescript
// PageDefault component
const PageDefault = ({ theme, paths, propsLayout, propsPage }) => {
  const { component: PageComponent, layout: LayoutComponent } = paths[theme] || {};
  
  if (!PageComponent) {
    return <NotFound />;
  }
  
  return (
    <LayoutComponent {...propsLayout}>
      <PageComponent {...propsPage} />
    </LayoutComponent>
  );
};
```

## 🔄 State Management

### Redux Store Configuration
```typescript
// store/store.ts
const makeConfiguredStore = reducer => {
  const sagaMiddleware = createSagaMiddleware();
  
  const store = configureStore({
    reducer,
    middleware: [sagaMiddleware],
  });
  
  store.sagaTask = sagaMiddleware.run(rootSaga);
  return store;
};

export const makeStore = () => {
  const reducer = combineReducers(rootReducer);
  const isServer = typeof window === "undefined";
  
  if (isServer) {
    return makeConfiguredStore(reducer);
  }
  
  const persistedReducer = persistReducer(persistConfig, reducer);
  const store = makeConfiguredStore(persistedReducer);
  store.__persistor = persistStore(store);
  return store;
};
```

### Reducer Structure
```typescript
// store/reducer.ts
const rootReducer = {
  common: CommonReducers,
  progressTracking: TrackingProgressReducer,
  autoSyncData: AutoSyncDataReducer,
  featureStore: HFeatureReducers.reducer,
  firebase: FirebaseReducers.reducer,
  layouts: LayoutReducer,
  system: SystemReducer,
  notification: NotificationReducer,
  curriculumPlayer: CurriculumReducer,
  coursePlayer: CoursePlayerSlice.reducer,
  telegramBot: TelegramBotReducer,
  k12Education: K12EducationReducer,
};
```

## 🌐 Internationalization

### i18n Configuration
```typescript
// lib/i18n/use-h-translation.ts
i18next
  .use(initReactI18next)
  .use(LanguageDetector)
  .use(resourcesToBackend((language, namespace) => 
    import(`public/locales/${language}/${namespace}.json`)
  ))
  .init({
    ...getOptions(),
    lng: undefined,
    detection: {
      order: ["path", "cookie"],
      lookupCookie: "NEXT_LOCALE",
      lookupFromPathIndex: 0,
    },
  });
```

### Translation Hook
```typescript
export const useHTranslation = (namespace: string, options?: UseTranslationOptions) => {
  const { t, ...trans } = useTranslation(namespace, options);
  const locale = useLocale();
  
  const translate = (key: any, defaultValue: any = {}, options?: any) => {
    const defaultKey = typeof defaultValue === "string" 
      ? defaultValue 
      : defaultValue[locale] || key;
    return t(key, defaultKey, options) || key;
  };
  
  return { t: translate, ...trans };
};
```

## 🔌 API Integration

### Server-Side Data Fetching
```typescript
// lib/networks/http/server-side/index.ts
export const nextjsFetchData = async ({
  params = {},
  nodeName = "",
  endpoint = "",
  method = "get",
  headers = {},
  locale,
  isInternalRequest = false,
}) => {
  const requestHeaders = {
    ...headers,
    locale,
  };
  
  if (isInternalRequest) {
    requestHeaders.cookies = getCookiesServerSide();
    requestHeaders.Authorization = `Bearer ${getCookieValueServerSide("h2token")?.value ?? ""}`;
  }
  
  const url = getNextjsFetchDataUrl({
    documentId,
    endpoint,
    featureId,
    method,
    nodeName,
    params,
    withRelations,
  });
  
  return doRequestServerSide({
    headers: requestHeaders,
    url,
    params,
  }, method);
};
```

### Client-Side HTTP Client
```typescript
// lib/networks/http/index.ts
const doInternalRequest = (networkData: INetwork, method: string) => {
  const token = getToken();
  const headers = {
    "Authorization": `Bearer ${token}`,
    "Content-Type": "application/json",
    ...networkData.headers,
  };
  
  return axios({
    method,
    url: networkData.url,
    data: networkData.params,
    headers,
  });
};
```

## 🎯 Component Architecture

### Feature-Based Organization
```
components/
├── features/              # Feature-specific components
│   ├── client/           # Client-side features
│   │   ├── home-medoo-v2/
│   │   ├── business-v3/
│   │   └── tether/
│   ├── admin/            # Admin features
│   └── instructor/       # Instructor features
├── shared/               # Shared components
│   ├── ui/              # UI components
│   ├── forms/           # Form components
│   └── layouts/         # Layout components
└── ui/                  # Base UI components
```

### Component Patterns
```typescript
// Higher-Order Component pattern
const withTheme = (WrappedComponent) => {
  return (props) => {
    const theme = useTheme();
    return <WrappedComponent {...props} theme={theme} />;
  };
};

// Render Props pattern
const DataProvider = ({ children, endpoint }) => {
  const [data, loading, error] = useApi(endpoint);
  return children({ data, loading, error });
};

// Compound Component pattern
const Modal = ({ children }) => {
  return <div className="modal">{children}</div>;
};
Modal.Header = ({ children }) => <div className="modal-header">{children}</div>;
Modal.Body = ({ children }) => <div className="modal-body">{children}</div>;
Modal.Footer = ({ children }) => <div className="modal-footer">{children}</div>;
```

## 🎨 Styling Architecture

### SCSS + Tailwind Hybrid
```scss
// styles/app.scss
@import 'antd-custom/index.scss';
@import 'shared/variables';
@import 'animations/index';

// Component-specific styles
.ui-h2platform-system {
  @apply min-h-screen bg-gray-50;
  
  .theme-medoo & {
    --primary-color: #2F57EF;
    --text-color: #111;
  }
  
  .theme-edumall & {
    --primary-color: #FF6B35;
    --text-color: #333;
  }
}
```

### Ant Design Customization
```typescript
// themes/register/medoo-web3/styles.tsx
export const MedooWeb3Theme = {
  token: {
    colorPrimary: "#2F57EF",
    colorText: "#111",
    lineHeight: 1.265,
    fontFamily: helveticaNeue.style.fontFamily,
    controlHeight: 44,
    borderRadius: 12,
    colorTextPlaceholder: "#5D6B98",
    colorBorder: "#DCDFEA",
  },
  hashed: true,
};
```

## 🔧 Development Tools

### TypeScript Configuration
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "components/*": ["./components/*"],
      "lib/*": ["./lib/*"],
      "styles/*": ["./styles/*"]
    }
  }
}
```

### ESLint Configuration
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    "next/core-web-vitals",
    "airbnb",
    "airbnb/hooks",
    "@typescript-eslint/recommended"
  ],
  rules: {
    "react/react-in-jsx-scope": "off",
    "react/prop-types": "off",
    "@typescript-eslint/no-unused-vars": "error",
    "import/prefer-default-export": "off"
  }
};
```

---

**Tiếp theo**: [03-telegram-bot-architecture.md](./03-telegram-bot-architecture.md) - Kiến trúc Telegram Bot
