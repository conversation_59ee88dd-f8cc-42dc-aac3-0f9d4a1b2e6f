# E-commerce Frontend Architecture với ShadcnUI

## 🎯 Tổng quan

Phân tích và thiết kế kiến trúc frontend cho hệ thống thương mại điện tử, tích hợp các thành phần có giá trị từ Medoo WebApp với ShadcnUI và các best practices hiện đại.

## 📊 E-commerce Requirements Analysis

### Core E-commerce Features
```typescript
// E-commerce specific requirements
interface EcommerceRequirements {
  // Product Management
  productCatalog: ProductListing | ProductDetail | ProductSearch;
  productVariants: ColorSize | Inventory | Pricing;

  // Shopping Experience
  shoppingCart: AddToCart | CartManagement | Checkout;
  wishlist: SaveForLater | ShareWishlist;

  // User Management
  authentication: Login | Register | SocialAuth;
  userProfile: OrderHistory | AddressBook | Preferences;

  // Order Management
  checkout: PaymentGateway | ShippingOptions | OrderConfirmation;
  orderTracking: OrderStatus | ShippingTracking;

  // Content Management
  cms: LandingPages | Promotions | SEO;
  reviews: ProductReviews | Ratings | UserGeneratedContent;
}
```

## 🔥 **Extraction Analysis cho E-commerce**

### **TIER 1 - CRITICAL cho E-commerce**

#### 1. Schema-Form System ⭐⭐⭐⭐⭐
**Adaptation cho ShadcnUI:**
```typescript
// Adapt schema-form để work với ShadcnUI
// packages/webapp/schema-form/ -> src/lib/form-builder/

// Before (Ant Design)
import { Input, Select, Button } from "antd";

// After (ShadcnUI)
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { Button } from "@/components/ui/button";

// Schema adapter
const createShadcnSchemaItem = (item: FormItemProps) => {
  return {
    ...item,
    Component: mapAntdToShadcn(item.Component),
    componentProps: adaptPropsForShadcn(item.componentProps)
  };
};
```

**E-commerce Use Cases:**
- ✅ **Checkout Forms**: Multi-step checkout với validation
- ✅ **Product Forms**: Add/edit products với variants
- ✅ **User Registration**: Account creation với validation
- ✅ **Address Forms**: Shipping/billing address management
- ✅ **Review Forms**: Product review submission

#### 2. Authentication System ⭐⭐⭐⭐⭐
**E-commerce Adaptation:**
```typescript
// Enhanced auth cho e-commerce
interface EcommerceAuthState {
  user: Customer | null;
  cart: CartState;
  wishlist: WishlistState;
  addresses: Address[];
  paymentMethods: PaymentMethod[];
  orderHistory: Order[];
}

// Auth hooks cho e-commerce
export const useCustomerAuth = () => {
  const { user, login, logout } = useAuth();

  const loginWithCart = async (credentials) => {
    const result = await login(credentials);
    // Merge guest cart với user cart
    await mergeGuestCart();
    return result;
  };

  return { user, loginWithCart, logout };
};
```

#### 3. State Management - Zustand thay Redux ⭐⭐⭐⭐⭐
**Tại sao Zustand cho E-commerce:**
```typescript
// E-commerce stores với Zustand
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Cart Store
export const useCartStore = create(
  persist(
    (set, get) => ({
      items: [],
      total: 0,

      addItem: (product, quantity = 1) => set((state) => {
        const existingItem = state.items.find(item => item.id === product.id);
        if (existingItem) {
          return {
            items: state.items.map(item =>
              item.id === product.id
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          };
        }
        return { items: [...state.items, { ...product, quantity }] };
      }),

      removeItem: (productId) => set((state) => ({
        items: state.items.filter(item => item.id !== productId)
      })),

      updateQuantity: (productId, quantity) => set((state) => ({
        items: state.items.map(item =>
          item.id === productId ? { ...item, quantity } : item
        )
      })),

      clearCart: () => set({ items: [], total: 0 }),

      calculateTotal: () => set((state) => ({
        total: state.items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
      }))
    }),
    { name: 'cart-storage' }
  )
);

// Product Store
export const useProductStore = create((set, get) => ({
  products: [],
  filters: {},
  searchQuery: '',
  loading: false,

  setProducts: (products) => set({ products }),
  setFilters: (filters) => set({ filters }),
  setSearchQuery: (query) => set({ searchQuery: query }),

  fetchProducts: async (params) => {
    set({ loading: true });
    try {
      const products = await productAPI.getProducts(params);
      set({ products, loading: false });
    } catch (error) {
      set({ loading: false });
    }
  }
}));
```

#### 4. API Layer với React Query ⭐⭐⭐⭐⭐
**Modern API Management:**
```typescript
// API layer với React Query cho e-commerce
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

// Product queries
export const useProducts = (filters) => {
  return useQuery({
    queryKey: ['products', filters],
    queryFn: () => productAPI.getProducts(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProduct = (productId) => {
  return useQuery({
    queryKey: ['product', productId],
    queryFn: () => productAPI.getProduct(productId),
    enabled: !!productId,
  });
};

// Cart mutations
export const useAddToCart = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: cartAPI.addItem,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cart'] });
    },
  });
};

// Order mutations
export const useCreateOrder = () => {
  return useMutation({
    mutationFn: orderAPI.createOrder,
    onSuccess: (order) => {
      // Clear cart after successful order
      useCartStore.getState().clearCart();
      // Redirect to order confirmation
      router.push(`/orders/${order.id}/confirmation`);
    },
  });
};
```

### **TIER 2 - IMPORTANT cho E-commerce**

#### 5. ShadcnUI Component Adaptations ⭐⭐⭐⭐
**E-commerce Specific Components:**
```typescript
// Product Card Component
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export const ProductCard = ({ product }) => {
  const addToCart = useCartStore(state => state.addItem);

  return (
    <Card className="group hover:shadow-lg transition-shadow">
      <CardContent className="p-0">
        <div className="relative overflow-hidden">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-48 object-cover group-hover:scale-105 transition-transform"
          />
          {product.discount && (
            <Badge className="absolute top-2 left-2" variant="destructive">
              -{product.discount}%
            </Badge>
          )}
        </div>
        <div className="p-4">
          <h3 className="font-semibold truncate">{product.name}</h3>
          <p className="text-sm text-muted-foreground">{product.category}</p>
          <div className="flex items-center justify-between mt-2">
            <span className="font-bold">${product.price}</span>
            {product.originalPrice && (
              <span className="text-sm line-through text-muted-foreground">
                ${product.originalPrice}
              </span>
            )}
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        <Button
          className="w-full"
          onClick={() => addToCart(product)}
        >
          Add to Cart
        </Button>
      </CardFooter>
    </Card>
  );
};

// Shopping Cart Drawer
import { Sheet, SheetContent, SheetHeader, SheetTitle } from "@/components/ui/sheet";

export const CartDrawer = ({ open, onClose }) => {
  const { items, total, updateQuantity, removeItem } = useCartStore();

  return (
    <Sheet open={open} onOpenChange={onClose}>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle>Shopping Cart ({items.length})</SheetTitle>
        </SheetHeader>
        <div className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto">
            {items.map(item => (
              <CartItem
                key={item.id}
                item={item}
                onUpdateQuantity={updateQuantity}
                onRemove={removeItem}
              />
            ))}
          </div>
          <div className="border-t pt-4">
            <div className="flex justify-between text-lg font-semibold">
              <span>Total: ${total}</span>
            </div>
            <Button className="w-full mt-4" size="lg">
              Checkout
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};
```

#### 6. Search & Filtering System ⭐⭐⭐⭐
**Advanced Product Search:**
```typescript
// Search với debouncing và filtering
import { useDeferredValue, useMemo } from 'react';
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";

export const ProductSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [priceRange, setPriceRange] = useState([0, 1000]);
  const [category, setCategory] = useState('');
  const [sortBy, setSortBy] = useState('name');

  const deferredQuery = useDeferredValue(searchQuery);

  const filters = useMemo(() => ({
    search: deferredQuery,
    priceMin: priceRange[0],
    priceMax: priceRange[1],
    category,
    sortBy
  }), [deferredQuery, priceRange, category, sortBy]);

  const { data: products, isLoading } = useProducts(filters);

  return (
    <div className="space-y-4">
      <Input
        placeholder="Search products..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Select value={category} onValueChange={setCategory}>
          <SelectTrigger>
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">All Categories</SelectItem>
            <SelectItem value="electronics">Electronics</SelectItem>
            <SelectItem value="clothing">Clothing</SelectItem>
          </SelectContent>
        </Select>

        <div className="space-y-2">
          <label className="text-sm font-medium">Price Range</label>
          <Slider
            value={priceRange}
            onValueChange={setPriceRange}
            max={1000}
            step={10}
            className="w-full"
          />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>${priceRange[0]}</span>
            <span>${priceRange[1]}</span>
          </div>
        </div>

        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger>
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">Name</SelectItem>
            <SelectItem value="price-asc">Price: Low to High</SelectItem>
            <SelectItem value="price-desc">Price: High to Low</SelectItem>
            <SelectItem value="rating">Rating</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <ProductGrid products={products} loading={isLoading} />
    </div>
  );
};
```

## 🏗️ **Recommended E-commerce Architecture**

### **1. Headless Commerce Architecture**
```typescript
// Modern headless e-commerce stack
interface HeadlessEcommerceStack {
  frontend: "Next.js 14 + ShadcnUI";
  backend: "Headless CMS (Strapi/Payload) + Commerce API";
  database: "PostgreSQL/MongoDB";
  payments: "Stripe/PayPal";
  search: "Algolia/Elasticsearch";
  cdn: "Vercel/Cloudflare";
  analytics: "Google Analytics 4";
}

// Project structure
src/
├── app/                    # Next.js 14 App Router
│   ├── (shop)/            # Shop routes group
│   │   ├── products/      # Product pages
│   │   ├── cart/          # Cart page
│   │   └── checkout/      # Checkout flow
│   ├── (account)/         # Account routes group
│   │   ├── profile/       # User profile
│   │   ├── orders/        # Order history
│   │   └── addresses/     # Address management
│   └── api/               # API routes
├── components/
│   ├── ui/                # ShadcnUI components
│   ├── ecommerce/         # E-commerce specific components
│   │   ├── product/       # Product components
│   │   ├── cart/          # Cart components
│   │   ├── checkout/      # Checkout components
│   │   └── user/          # User components
│   └── shared/            # Shared components (from Medoo)
├── lib/
│   ├── api/               # API layer
│   ├── stores/            # Zustand stores
│   ├── hooks/             # Custom hooks
│   ├── utils/             # Utilities (from Medoo)
│   └── form-builder/      # Schema-form system (adapted)
├── styles/                # Global styles
└── types/                 # TypeScript types
```

### **2. State Management Strategy**
```typescript
// Hybrid state management approach
interface StateManagementStrategy {
  // Client state: Zustand
  cart: "Zustand + Persist";
  ui: "Zustand (modals, drawers, etc)";
  user: "Zustand + Persist";

  // Server state: React Query
  products: "React Query";
  orders: "React Query";
  reviews: "React Query";

  // Form state: React Hook Form + Zod
  forms: "React Hook Form + Schema validation";
}

// Store organization
stores/
├── cart.store.ts          # Shopping cart state
├── user.store.ts          # User preferences & auth
├── ui.store.ts            # UI state (modals, drawers)
└── wishlist.store.ts      # Wishlist state
```

### **3. Performance Optimization**
```typescript
// Performance strategies cho e-commerce
interface PerformanceStrategy {
  // Image optimization
  images: "Next.js Image + WebP + Lazy loading";

  // Code splitting
  routing: "Route-based splitting";
  components: "Dynamic imports for heavy components";

  // Caching
  api: "React Query + SWR";
  static: "Next.js ISR";
  cdn: "Vercel Edge Network";

  // SEO
  metadata: "Next.js Metadata API";
  sitemap: "Dynamic sitemap generation";
  structured: "JSON-LD for products";
}

// Example: Product page optimization
export default async function ProductPage({ params }) {
  // Static generation cho popular products
  const product = await getProduct(params.id);

  return (
    <div>
      <ProductImages images={product.images} />
      <ProductInfo product={product} />
      <Suspense fallback={<ReviewsSkeleton />}>
        <ProductReviews productId={product.id} />
      </Suspense>
      <Suspense fallback={<RecommendationsSkeleton />}>
        <RelatedProducts categoryId={product.categoryId} />
      </Suspense>
    </div>
  );
}

// Generate static paths cho popular products
export async function generateStaticParams() {
  const popularProducts = await getPopularProducts();
  return popularProducts.map(product => ({
    id: product.id.toString()
  }));
}
```

## 🚀 **Implementation Roadmap**

### **Phase 1: Foundation (2-3 weeks)**
```bash
# 1. Setup Next.js 14 + ShadcnUI
npx create-next-app@latest ecommerce-frontend --typescript --tailwind --app

# 2. Install dependencies
npm install @tanstack/react-query zustand @hookform/react-hook-form zod

# 3. Extract core utilities from Medoo
cp -r packages/webapp/lib/utils/ src/lib/
cp -r packages/webapp/lib/hooks/ src/lib/
cp -r packages/webapp/lib/i18n/ src/lib/

# 4. Adapt authentication system
cp -r packages/webapp/lib/providers/auth* src/lib/providers/
# Modify để work với Zustand thay vì Redux
```

### **Phase 2: Core E-commerce (3-4 weeks)**
```bash
# 5. Adapt schema-form system cho ShadcnUI
cp -r packages/webapp/schema-form/ src/lib/form-builder/
# Modify components để use ShadcnUI

# 6. Build core e-commerce components
# - Product catalog
# - Shopping cart
# - User authentication
# - Basic checkout

# 7. Setup state management
# - Zustand stores
# - React Query setup
# - Form validation với Zod
```

### **Phase 3: Advanced Features (4-5 weeks)**
```bash
# 8. Advanced search & filtering
# 9. Payment integration
# 10. Order management
# 11. User dashboard
# 12. Admin panel (if needed)
```

### **Phase 4: Optimization (2-3 weeks)**
```bash
# 13. Performance optimization
# 14. SEO implementation
# 15. Analytics integration
# 16. Testing & deployment
```

## 📊 **Expected Benefits**

### **Development Speed:**
- **Schema-form adaptation**: 60% faster form development
- **Auth system reuse**: 80% faster auth implementation
- **Utility functions**: 50% faster common functionality
- **Component patterns**: 40% faster UI development

### **Code Quality:**
- **Type Safety**: Full TypeScript support
- **Testing**: Proven patterns from production
- **Performance**: Optimized components
- **Accessibility**: ShadcnUI accessibility standards

### **Maintenance:**
- **Consistent patterns**: Easier onboarding
- **Modular architecture**: Easy to extend
- **Modern stack**: Future-proof technology choices

## 🔧 **Specific Extraction Guide cho E-commerce**

### **Files cần Extract từ Medoo:**

#### **CRITICAL - Phải có:**
```bash
# 1. Schema-form system (adapt cho ShadcnUI)
packages/webapp/schema-form/
├── h-form.tsx              # Core form wrapper
├── h-types.tsx             # Type definitions
├── utils/form-utils.tsx    # Form utilities
└── features/hooks/         # Form hooks

# 2. Authentication system
packages/webapp/lib/providers/
├── auth.tsx                # Auth provider
├── auth-hook/              # Auth hooks
└── cookies-method/         # Cookie management

# 3. API & Networking
packages/webapp/lib/networks/
├── http/index.ts           # HTTP client
├── endpoints.ts            # API endpoints
└── error-processing.ts     # Error handling

# 4. Core utilities
packages/webapp/lib/utils/
├── authentication-utils.ts # Auth utilities
├── currency-utils.ts       # Currency formatting
├── formatter/index.ts      # Data formatting
└── image/index.ts          # Image utilities

# 5. i18n system
packages/webapp/lib/i18n/
├── use-h-translation.ts    # Translation hook
└── get-translation-func.ts # Translation function
```

#### **IMPORTANT - Nên có:**
```bash
# 6. Common form elements (adapt cho ShadcnUI)
packages/webapp/components/shared/common-form-elements/
├── h-upload/               # File upload
├── h-input/                # Enhanced inputs
└── select/                 # Advanced selects

# 7. Common hooks
packages/webapp/lib/hooks/
├── use-media.ts            # Responsive hooks
├── use-mobile-detect.ts    # Mobile detection
└── multi-language/         # i18n hooks

# 8. Layout utilities
packages/webapp/components/shared/layout/
├── router-contaner/        # Router utilities
└── panel/                  # Panel components
```

### **Adaptation Strategy cho ShadcnUI:**

#### **1. Form System Adaptation:**
```typescript
// Create adapter layer
// src/lib/form-builder/shadcn-adapter.ts

import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";

const COMPONENT_MAP = {
  'input': Input,
  'select': Select,
  'textarea': Textarea,
  'button': Button,
  // Add more mappings
};

export const adaptSchemaForShadcn = (schema) => {
  return schema.map(item => ({
    ...item,
    Component: COMPONENT_MAP[item.type] || item.Component,
    componentProps: adaptPropsForShadcn(item.componentProps)
  }));
};

// Usage in e-commerce forms
const checkoutSchema = adaptSchemaForShadcn([
  {
    type: 'input',
    name: 'email',
    label: 'Email Address',
    rules: [{ required: true, type: 'email' }]
  },
  {
    type: 'select',
    name: 'country',
    label: 'Country',
    options: countryOptions
  }
]);
```

#### **2. State Management Migration:**
```typescript
// Migrate từ Redux sang Zustand
// src/stores/cart.store.ts

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  variant?: {
    size?: string;
    color?: string;
  };
}

interface CartStore {
  items: CartItem[];
  total: number;
  itemCount: number;

  // Actions
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;

  // Computed
  getItemById: (productId: string) => CartItem | undefined;
  calculateTotal: () => void;
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      total: 0,
      itemCount: 0,

      addItem: (product, quantity = 1) => {
        const { items } = get();
        const existingItem = items.find(item => item.id === product.id);

        if (existingItem) {
          set({
            items: items.map(item =>
              item.id === product.id
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          });
        } else {
          set({
            items: [...items, {
              id: product.id,
              name: product.name,
              price: product.price,
              quantity,
              image: product.image
            }]
          });
        }

        get().calculateTotal();
      },

      removeItem: (productId) => {
        set({
          items: get().items.filter(item => item.id !== productId)
        });
        get().calculateTotal();
      },

      updateQuantity: (productId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(productId);
          return;
        }

        set({
          items: get().items.map(item =>
            item.id === productId ? { ...item, quantity } : item
          )
        });
        get().calculateTotal();
      },

      clearCart: () => set({ items: [], total: 0, itemCount: 0 }),

      getItemById: (productId) => {
        return get().items.find(item => item.id === productId);
      },

      calculateTotal: () => {
        const { items } = get();
        const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
        set({ total, itemCount });
      }
    }),
    {
      name: 'cart-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
```

### **Technology Stack Recommendations:**

#### **Core Stack:**
```json
{
  "framework": "Next.js 14 (App Router)",
  "ui": "ShadcnUI + Tailwind CSS",
  "state": "Zustand + React Query",
  "forms": "React Hook Form + Zod",
  "auth": "NextAuth.js (adapted from Medoo auth)",
  "payments": "Stripe + PayPal",
  "search": "Algolia or local search",
  "analytics": "Google Analytics 4 + Vercel Analytics"
}
```

#### **Development Tools:**
```json
{
  "typescript": "^5.0.0",
  "eslint": "^8.0.0",
  "prettier": "^3.0.0",
  "husky": "^8.0.0",
  "lint-staged": "^13.0.0",
  "testing": "Jest + React Testing Library"
}
```

### **Performance Considerations:**

#### **Bundle Size Optimization:**
```typescript
// Dynamic imports cho heavy components
const ProductReviews = dynamic(() => import('./ProductReviews'), {
  loading: () => <ReviewsSkeleton />,
  ssr: false
});

const CheckoutForm = dynamic(() => import('./CheckoutForm'), {
  loading: () => <CheckoutSkeleton />
});

// Code splitting by routes
const AdminPanel = dynamic(() => import('./AdminPanel'), {
  ssr: false
});
```

#### **Image Optimization:**
```typescript
// Next.js Image với optimization
import Image from 'next/image';

export const ProductImage = ({ src, alt, ...props }) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={400}
      height={400}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      priority={props.priority}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
      {...props}
    />
  );
};
```

### **SEO & Marketing Features:**
```typescript
// Structured data cho products
export const ProductStructuredData = ({ product }) => {
  const structuredData = {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": product.name,
    "image": product.images,
    "description": product.description,
    "sku": product.sku,
    "brand": {
      "@type": "Brand",
      "name": product.brand
    },
    "offers": {
      "@type": "Offer",
      "url": `${process.env.NEXT_PUBLIC_SITE_URL}/products/${product.slug}`,
      "priceCurrency": "USD",
      "price": product.price,
      "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};
```

---

**Kết luận**: Kết hợp Medoo's proven patterns với modern e-commerce architecture và ShadcnUI sẽ tạo ra một frontend mạnh mẽ, scalable và maintainable cho hệ thống thương mại điện tử. Estimated development time: 12-16 weeks với team 2-3 developers.
