# State Management - Quản lý State

## 🎯 Tổng quan

Hệ thống sử dụng nhiều giải pháp state management khác nhau tùy theo từng package và use case cụ thể. Mỗi approach có ưu điểm riêng và được áp dụng phù hợp với yêu cầu của từng ứng dụng.

## 📊 State Management Strategies

### 1. WebApp - Redux Toolkit + Redux Saga
**Sử dụng cho**: Complex state logic, async operations, side effects
**Ưu điểm**: Predictable, debuggable, time-travel debugging
**Nhược điểm**: Boilerplate code, learning curve

### 2. Telegram Bot - Hookstate
**Sử dụng cho**: Simple to medium complexity, reactive updates
**Ưu điểm**: Minimal boilerplate, reactive, TypeScript-friendly
**Nhược điểm**: Smaller ecosystem, less tooling

### 3. Landing Page - Redux Toolkit
**Sử dụng cho**: Web3 state, user authentication, form state
**Ưu điểm**: Familiar API, good DevTools, persistence support
**Nhược điểm**: Overkill for simple state

## 🔄 WebApp State Management (Redux)

### Store Configuration
```typescript
// store/store.ts
import { combineReducers } from "redux";
import createSagaMiddleware from "@redux-saga/core";
import { configureStore } from "@reduxjs/toolkit";
import { persistReducer, persistStore } from "redux-persist";
import storage from "redux-persist/lib/storage";

const persistConfig = {
  key: "persist",
  storage,
  whitelist: ["auth", "user", "settings"], // Only persist these reducers
  blacklist: ["ui", "temp"], // Don't persist these
};

const makeConfiguredStore = (reducer: any) => {
  const sagaMiddleware = createSagaMiddleware();

  const store = configureStore({
    reducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
        },
      }).concat(sagaMiddleware),
    devTools: process.env.NODE_ENV !== "production",
  });

  store.sagaTask = sagaMiddleware.run(rootSaga);
  return store;
};

export const makeStore = () => {
  const reducer = combineReducers(rootReducer);
  const isServer = typeof window === "undefined";

  if (isServer) {
    return makeConfiguredStore(reducer);
  }

  const persistedReducer = persistReducer(persistConfig, reducer);
  const store = makeConfiguredStore(persistedReducer);
  store.__persistor = persistStore(store);
  return store;
};
```

### Reducer Structure
```typescript
// store/reducer.ts
const rootReducer = {
  // Core reducers
  auth: AuthSlice.reducer,
  user: UserSlice.reducer,
  ui: UISlice.reducer,
  
  // Feature reducers
  courses: CoursesSlice.reducer,
  notifications: NotificationSlice.reducer,
  payments: PaymentSlice.reducer,
  
  // System reducers
  system: SystemReducer,
  layouts: LayoutReducer,
  common: CommonReducers,
  
  // Tracking & Analytics
  progressTracking: TrackingProgressReducer,
  viewportTracking: ViewportTrackingSlice.reducer,
  
  // Auto-sync data
  autoSyncData: AutoSyncDataReducer,
  
  // Feature stores
  featureStore: HFeatureReducers.reducer,
  documentViewers: HDvReducers.reducer,
  
  // Platform-specific
  telegramBot: TelegramBotReducer,
  k12Education: K12EducationReducer,
  
  // Players
  curriculumPlayer: CurriculumReducer,
  coursePlayer: CoursePlayerSlice.reducer,
  questionPlayers: QuestionPlayersSlice,
  learningPathPlayer: LearningPathPlayer.reducer,
  
  // External integrations
  firebase: FirebaseReducers.reducer,
};
```

### Auth Slice Example
```typescript
// store/slices/authSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";

interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  loginAttempts: number;
  lastLoginAt: string | null;
}

const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginAttempts: 0,
  lastLoginAt: null,
};

// Async thunks
export const loginUser = createAsyncThunk(
  "auth/loginUser",
  async (
    credentials: { email: string; password: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await authAPI.login(credentials);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || "Login failed");
    }
  }
);

export const refreshAuthToken = createAsyncThunk(
  "auth/refreshToken",
  async (_, { getState, rejectWithValue }) => {
    try {
      const { auth } = getState() as { auth: AuthState };
      const response = await authAPI.refreshToken(auth.refreshToken!);
      return response.data;
    } catch (error: any) {
      return rejectWithValue("Token refresh failed");
    }
  }
);

export const logoutUser = createAsyncThunk(
  "auth/logoutUser",
  async (_, { getState }) => {
    const { auth } = getState() as { auth: AuthState };
    if (auth.token) {
      await authAPI.logout(auth.token);
    }
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
        state.isAuthenticated = true;
        state.loginAttempts = 0;
        state.lastLoginAt = new Date().toISOString();
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
        state.loginAttempts += 1;
      })
      // Refresh token
      .addCase(refreshAuthToken.fulfilled, (state, action) => {
        state.token = action.payload.token;
        state.refreshToken = action.payload.refreshToken;
      })
      .addCase(refreshAuthToken.rejected, (state) => {
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
      })
      // Logout
      .addCase(logoutUser.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.refreshToken = null;
        state.isAuthenticated = false;
        state.error = null;
      });
  },
});

export const { clearError, updateUser, resetLoginAttempts } = authSlice.actions;
export default authSlice;
```

### Saga for Side Effects
```typescript
// sagas/authSaga.ts
import { call, put, takeEvery, delay, select } from "redux-saga/effects";
import { PayloadAction } from "@reduxjs/toolkit";
import { loginUser, refreshAuthToken, logoutUser } from "../store/slices/authSlice";

function* handleLoginSuccess(action: PayloadAction<any>) {
  try {
    // Set token in axios headers
    yield call(setAuthToken, action.payload.token);
    
    // Track login event
    yield call(analytics.track, "user_login", {
      userId: action.payload.user.id,
      timestamp: new Date().toISOString(),
    });
    
    // Redirect to dashboard
    yield call(router.push, "/dashboard");
    
    // Show success notification
    yield put(showNotification({
      type: "success",
      message: "Login successful!",
    }));
  } catch (error) {
    console.error("Login success handler error:", error);
  }
}

function* handleTokenRefresh() {
  try {
    const authState = yield select((state: RootState) => state.auth);
    
    if (!authState.refreshToken) {
      yield put(logoutUser());
      return;
    }
    
    // Auto-refresh token 5 minutes before expiry
    yield delay(25 * 60 * 1000); // 25 minutes
    yield put(refreshAuthToken());
  } catch (error) {
    yield put(logoutUser());
  }
}

function* handleLogout() {
  try {
    // Clear token from axios headers
    yield call(removeAuthToken);
    
    // Clear local storage
    yield call(localStorage.clear);
    
    // Track logout event
    yield call(analytics.track, "user_logout");
    
    // Redirect to login
    yield call(router.push, "/login");
  } catch (error) {
    console.error("Logout handler error:", error);
  }
}

export function* authSaga() {
  yield takeEvery(loginUser.fulfilled.type, handleLoginSuccess);
  yield takeEvery(loginUser.fulfilled.type, handleTokenRefresh);
  yield takeEvery(logoutUser.fulfilled.type, handleLogout);
}
```

## 🎣 Telegram Bot State Management (Hookstate)

### Game State Hook
```typescript
// lib/hook/gameState.ts
import { hookstate, useHookstate, useHookstateEffect } from "@hookstate/core";

interface GameState {
  // Player stats
  currentPoint: number;
  level: number;
  experience: number;
  
  // Game mechanics
  energy: number;
  maxEnergy: number;
  energyRegenRate: number;
  lastEnergyUpdate: number;
  
  // Earnings
  profitPerTap: number;
  profitPerSecond: number;
  totalEarned: number;
  
  // Boosts
  activeBoosts: Boost[];
  boostMultiplier: number;
  
  // Inventory
  items: GameItem[];
  upgrades: Upgrade[];
  
  // Social
  friends: Friend[];
  leaderboardPosition: number;
}

const gameState = hookstate<GameState>({
  currentPoint: 0,
  level: 1,
  experience: 0,
  energy: 100,
  maxEnergy: 100,
  energyRegenRate: 1,
  lastEnergyUpdate: Date.now(),
  profitPerTap: 1,
  profitPerSecond: 0,
  totalEarned: 0,
  activeBoosts: [],
  boostMultiplier: 1,
  items: [],
  upgrades: [],
  friends: [],
  leaderboardPosition: 0,
});

export const useGameState = () => {
  const state = useHookstate(gameState);
  
  // Energy regeneration effect
  useHookstateEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      const timeDiff = now - state.lastEnergyUpdate.get();
      const energyToAdd = Math.floor(timeDiff / 1000) * state.energyRegenRate.get();
      
      if (energyToAdd > 0 && state.energy.get() < state.maxEnergy.get()) {
        const newEnergy = Math.min(
          state.energy.get() + energyToAdd,
          state.maxEnergy.get()
        );
        state.energy.set(newEnergy);
        state.lastEnergyUpdate.set(now);
      }
    }, 1000);
    
    return () => clearInterval(interval);
  }, []);
  
  // Passive income effect
  useHookstateEffect(() => {
    if (state.profitPerSecond.get() > 0) {
      const interval = setInterval(() => {
        const profit = state.profitPerSecond.get() * state.boostMultiplier.get();
        state.currentPoint.set(prev => prev + profit);
        state.totalEarned.set(prev => prev + profit);
      }, 1000);
      
      return () => clearInterval(interval);
    }
  }, [state.profitPerSecond.get(), state.boostMultiplier.get()]);
  
  const tapToEarn = () => {
    const currentEnergy = state.energy.get();
    if (currentEnergy <= 0) return false;
    
    const profit = state.profitPerTap.get() * state.boostMultiplier.get();
    
    state.currentPoint.set(prev => prev + profit);
    state.totalEarned.set(prev => prev + profit);
    state.energy.set(prev => Math.max(0, prev - 1));
    state.experience.set(prev => prev + 1);
    
    // Level up check
    const expForNextLevel = state.level.get() * 100;
    if (state.experience.get() >= expForNextLevel) {
      state.level.set(prev => prev + 1);
      state.experience.set(0);
      state.maxEnergy.set(prev => prev + 10);
      state.energy.set(state.maxEnergy.get());
    }
    
    return true;
  };
  
  const purchaseUpgrade = (upgrade: Upgrade) => {
    const currentPoints = state.currentPoint.get();
    if (currentPoints < upgrade.cost) return false;
    
    state.currentPoint.set(prev => prev - upgrade.cost);
    state.upgrades.set(prev => [...prev, upgrade]);
    
    // Apply upgrade effects
    switch (upgrade.type) {
      case "tap_power":
        state.profitPerTap.set(prev => prev + upgrade.value);
        break;
      case "passive_income":
        state.profitPerSecond.set(prev => prev + upgrade.value);
        break;
      case "energy_capacity":
        state.maxEnergy.set(prev => prev + upgrade.value);
        break;
    }
    
    return true;
  };
  
  const activateBoost = (boost: Boost) => {
    const existingBoost = state.activeBoosts.get().find(b => b.id === boost.id);
    if (existingBoost) return false;
    
    const boostWithExpiry = {
      ...boost,
      expiresAt: Date.now() + boost.duration * 1000,
    };
    
    state.activeBoosts.set(prev => [...prev, boostWithExpiry]);
    state.boostMultiplier.set(prev => prev * boost.multiplier);
    
    // Auto-remove boost when expired
    setTimeout(() => {
      state.activeBoosts.set(prev => prev.filter(b => b.id !== boost.id));
      state.boostMultiplier.set(prev => prev / boost.multiplier);
    }, boost.duration * 1000);
    
    return true;
  };
  
  return {
    // State getters
    ...state.get(),
    
    // Actions
    tapToEarn,
    purchaseUpgrade,
    activateBoost,
    
    // Computed values
    energyPercentage: (state.energy.get() / state.maxEnergy.get()) * 100,
    expToNextLevel: state.level.get() * 100 - state.experience.get(),
    canTap: state.energy.get() > 0,
  };
};
```

### Persistent State Hook
```typescript
// lib/hook/persistentState.ts
import { hookstate, useHookstate } from "@hookstate/core";
import { Persistence } from "@hookstate/persistence";

interface PersistentGameData {
  userId: string;
  gameProgress: any;
  settings: UserSettings;
  achievements: Achievement[];
  lastSyncAt: number;
}

const persistentState = hookstate<PersistentGameData>({
  userId: "",
  gameProgress: {},
  settings: {
    soundEnabled: true,
    vibrationEnabled: true,
    language: "en",
    theme: "dark",
  },
  achievements: [],
  lastSyncAt: 0,
});

// Add persistence plugin
persistentState.attach(Persistence("telegram-bot-data"));

export const usePersistentState = () => {
  const state = useHookstate(persistentState);
  
  const saveGameProgress = (progress: any) => {
    state.gameProgress.set(progress);
    state.lastSyncAt.set(Date.now());
  };
  
  const updateSettings = (newSettings: Partial<UserSettings>) => {
    state.settings.merge(newSettings);
  };
  
  const unlockAchievement = (achievement: Achievement) => {
    const existing = state.achievements.get().find(a => a.id === achievement.id);
    if (!existing) {
      state.achievements.set(prev => [...prev, achievement]);
      return true;
    }
    return false;
  };
  
  const syncWithServer = async () => {
    try {
      const data = state.get();
      await api.syncGameData(data);
      state.lastSyncAt.set(Date.now());
    } catch (error) {
      console.error("Sync failed:", error);
    }
  };
  
  return {
    ...state.get(),
    saveGameProgress,
    updateSettings,
    unlockAchievement,
    syncWithServer,
  };
};
```

## 🌐 Landing Page State Management (Redux Toolkit)

### Web3 Slice
```typescript
// store/slices/web3Slice.ts
import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";

interface Web3State {
  // Wallet connection
  isConnected: boolean;
  address: string | null;
  chainId: number | null;
  balance: string;
  
  // Token data
  tokenBalance: string;
  stakedAmount: string;
  pendingRewards: string;
  
  // Transaction state
  pendingTransactions: Transaction[];
  transactionHistory: Transaction[];
  
  // Contract state
  contractData: ContractData | null;
  
  // UI state
  isLoading: boolean;
  error: string | null;
}

const initialState: Web3State = {
  isConnected: false,
  address: null,
  chainId: null,
  balance: "0",
  tokenBalance: "0",
  stakedAmount: "0",
  pendingRewards: "0",
  pendingTransactions: [],
  transactionHistory: [],
  contractData: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const connectWallet = createAsyncThunk(
  "web3/connectWallet",
  async (_, { rejectWithValue }) => {
    try {
      const { address, chainId } = await walletService.connect();
      const balance = await walletService.getBalance(address);
      return { address, chainId, balance };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

export const stakeTokens = createAsyncThunk(
  "web3/stakeTokens",
  async (amount: string, { getState, rejectWithValue }) => {
    try {
      const { web3 } = getState() as { web3: Web3State };
      const txHash = await stakingContract.stake(amount, web3.address!);
      return { amount, txHash };
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);

const web3Slice = createSlice({
  name: "web3",
  initialState,
  reducers: {
    setWalletConnected: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },
    setWalletAddress: (state, action: PayloadAction<string | null>) => {
      state.address = action.payload;
    },
    updateBalance: (state, action: PayloadAction<string>) => {
      state.balance = action.payload;
    },
    addPendingTransaction: (state, action: PayloadAction<Transaction>) => {
      state.pendingTransactions.push(action.payload);
    },
    removePendingTransaction: (state, action: PayloadAction<string>) => {
      state.pendingTransactions = state.pendingTransactions.filter(
        tx => tx.hash !== action.payload
      );
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(connectWallet.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(connectWallet.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isConnected = true;
        state.address = action.payload.address;
        state.chainId = action.payload.chainId;
        state.balance = action.payload.balance;
      })
      .addCase(connectWallet.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      .addCase(stakeTokens.fulfilled, (state, action) => {
        state.pendingTransactions.push({
          hash: action.payload.txHash,
          type: "stake",
          amount: action.payload.amount,
          timestamp: Date.now(),
          status: "pending",
        });
      });
  },
});

export const {
  setWalletConnected,
  setWalletAddress,
  updateBalance,
  addPendingTransaction,
  removePendingTransaction,
  clearError,
} = web3Slice.actions;

export default web3Slice.reducer;
```

## 🔧 State Management Best Practices

### 1. State Normalization
```typescript
// Normalize nested data structures
interface NormalizedState {
  users: {
    byId: Record<string, User>;
    allIds: string[];
  };
  courses: {
    byId: Record<string, Course>;
    allIds: string[];
  };
  enrollments: {
    byId: Record<string, Enrollment>;
    allIds: string[];
    byUserId: Record<string, string[]>;
    byCourseId: Record<string, string[]>;
  };
}
```

### 2. Selector Patterns
```typescript
// Memoized selectors
import { createSelector } from "@reduxjs/toolkit";

const selectUsers = (state: RootState) => state.users;
const selectCourses = (state: RootState) => state.courses;

export const selectUserById = createSelector(
  [selectUsers, (state: RootState, userId: string) => userId],
  (users, userId) => users.byId[userId]
);

export const selectEnrolledCourses = createSelector(
  [selectCourses, selectEnrollments, (state: RootState, userId: string) => userId],
  (courses, enrollments, userId) => {
    const userEnrollments = enrollments.byUserId[userId] || [];
    return userEnrollments.map(enrollmentId => {
      const enrollment = enrollments.byId[enrollmentId];
      return courses.byId[enrollment.courseId];
    });
  }
);
```

### 3. Error Handling
```typescript
// Centralized error handling
const createAsyncThunkWithErrorHandling = <T, R>(
  typePrefix: string,
  payloadCreator: (arg: T) => Promise<R>
) => {
  return createAsyncThunk(typePrefix, async (arg: T, { rejectWithValue }) => {
    try {
      return await payloadCreator(arg);
    } catch (error: any) {
      // Log error
      console.error(`${typePrefix} error:`, error);
      
      // Track error
      analytics.track("error", {
        action: typePrefix,
        error: error.message,
      });
      
      return rejectWithValue({
        message: error.message,
        code: error.code,
        timestamp: Date.now(),
      });
    }
  });
};
```

---

**Tiếp theo**: [07-ui-design-system.md](./07-ui-design-system.md) - Hệ thống UI/UX
