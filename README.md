# Medoo Platform - Frontend Architecture Documentation

## 📋 Tài liệu Frontend Architecture

Đây là tài liệu tổng hợp về kiến trúc frontend củ<PERSON> hệ thống Medoo Platform - một nền tảng giáo dục đa dạng với nhiều theme và ứng dụng khác nhau.

### 📚 Danh sách tài liệu:

1. [**01-overview.md**](./docs/01-overview.md) - Tổng quan hệ thống
2. [**02-webapp-architecture.md**](./docs/02-webapp-architecture.md) - <PERSON><PERSON>n trúc WebApp (Next.js)
3. [**03-telegram-bot-architecture.md**](./docs/03-telegram-bot-architecture.md) - <PERSON><PERSON>n trúc Telegram Bot (Vite + React)
4. [**04-landing-page-architecture.md**](./docs/04-landing-page-architecture.md) - <PERSON><PERSON><PERSON> trúc <PERSON> Page (Vite + React)
5. [**05-shared-libraries.md**](./docs/05-shared-libraries.md) - Thư viện chia sẻ
6. [**06-state-management.md**](./docs/06-state-management.md) - Quản lý state

### 🏗️ Kiến trúc tổng quan:

- **WebApp**: Next.js 14 + Redux Toolkit + Ant Design
- **Telegram Bot**: Vite + React + Hookstate + Ant Design Mobile
- **Landing Page**: Vite + React + Redux Toolkit + Web3 Integration
- **Shared Libraries**: TypeScript utilities, constants, types

### 🎨 Công nghệ chính:

- **Frontend**: React 18, Next.js 14, TypeScript, Vite
- **UI Libraries**: Ant Design, Ant Design Mobile, Tailwind CSS
- **State Management**: Redux Toolkit, Hookstate, Redux Saga
- **Web3**: Wagmi, Viem, ConnectKit, TON Connect
- **Build Tools**: Webpack 5, SWC, ESLint, Prettier

## Pre-requisites

```
Node.js v18.15.0
Redis
RabbitMQ
MongoDB 5.0
Yarn
```

## Setup the project
1. Run `yarn` in the root folder
2. Go to `./packages/sharing` and run `yarn build` to build the essential sharing libraries
3. There are 3 types of evironments and each `.env` must be named with the following format:
- develop: `.env.development`
- test: `.env.test`
- production: `.env.production`
4. Setup api-server
- Copy file `env.example` to `.env.[your evironment]`
- Replace the desire development env to your liking
- There is something worth notice to your `.env`:
  -  if you haven't installed replica set for monggodb, you have to comemnt `MONGODB_URL` in`.env.[your evironment]`
  - `INIT_SYSTEM_ORG_CODE` and `INIT_SYSTEM_USER_CODE` must be equal to `code` and `owner code` in `packages/api-server/fixtures/organizations/root.org.[your environment].yml`
  - If `FILE_SYSTEM_ADAPTER = filesystem` meaning that when client uploads, the file will store directly to the server.
  - If `FILE_SYSTEM_ADAPTER = aws`, you will need to configure the `AWS S3 configuration`
  - Not recommend `FILE_SYSTEM_ADAPTER = google` because it is not optimized yet
  - Must configure all the databases (redis, mongodb) configuration and `RABBIT_MQ_CONNECTION` otherwise the api-server cannot start
  - `MESSAGE_RECIPIENT_FIXED_EMAIL` is the email address that will be used to receive all the messages from the server when in `develop` environment
  - You must configure the `SMTP transport` in order to send emails from server

## Saas for local
There are two ways to run the project
 - Run `sudo nano /private/etc/hosts`
 - add the line: `127.0.0.1       *.localhost`
after that, you will get sub local domain point to localhost
 - tip: `127.0.0.1       *.medoo.dev` is good Idea

## Run the project
There are two ways to run the project
### Run in the same terminal
- In the root folder
- Run `./bin/start dev`

### Run seperately
- Run backend-server
  - In the root folder
  - Run
  ```console
  ./bin/start_backend
  ```
When you run bin/start_backend, it will default to starting 3 servers (api:3000, worker:3001)

If you want to run each server
- Run api-server
  - In the root folder
  - Run
  ```console
  ./bin/start_api
  ```

- Run worker-server
  - In the root folder
  - Run
  ```console
  ./bin/start_worker

- Run web app
  - In the root folder
  - Go to `packages/webapp`
  - Run
  ```consle
  yarn start
  ```
  or:
  - In the root folder
  - Run
    ```console
    ./bin/start_frontend
-

## Usage

The main app will be running at http://localhost.

The api will running http://localhost:3000/, and the API Explorer at
http://localhost:3000/explorer/.
### Go to admin page

- Go to http://localhost/users/login
- Enter the `email` and `password` from `packages/api-server/fixtures/organizations/root.org.[your environment].yml` and login
- Go to http://localhost/admin to access the admin page

### Running test

1. api-server
Run unit tests
- The unit tests is located in `packages/api-server/src/__tests__/*` folder
- In the terminal go to `packages/api-server`
- Run
```console
yarn test
```

2. Cucumber test
- In the root folder
- Run
```console
bin/start_cucumber
```

## Build translations

- All locale files are located at `packages/webapp/public/locales/`. Those translation files are automatically generated from [Medoo Translations](https://remi.group/medoo-translations) (edit in the
  Spreadshet, then run the script to rebuild those files).

- First & Once time (install ruby 2.7.3 and gems)
```
./bin/setup
```

- Command to rebuild locale translations (Press ENTER to rebuild
    locales, any key to stop)

```
./ruby-scripts/translation build
```

- Generate spreadsheet from current locale json files

```
./ruby-scripts/json_translations_to_sheet build -p packages/webapp/public/locales/en/admin-common.json
```

### Setup wrk - HTTP benchmarking
- For Ubuntu only, run script bellow
```
sudo apt-get install build-essential libssl-dev git -y
```
- Ubuntu and Macos run the script:
```
git clone https://github.com/wg/wrk.git wrk
cd wrk
sudo make
sudo cp wrk /usr/local/bin
```

### How to test
wrk -t30 -c2000 -d20s -s ./benchmarking-scripts/publi-course-list.lua http://localhost:3000

### Setup Luascript
curl -R -O http://www.lua.org/ftp/lua-5.4.6.tar.gz
tar zxf lua-5.4.6.tar.gz
cd lua-5.4.6
make all test
make install
