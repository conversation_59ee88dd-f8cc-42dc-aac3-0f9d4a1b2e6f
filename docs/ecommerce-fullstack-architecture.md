# E-commerce Fullstack Architecture - React 18 + Next.js 14 + Nest.js + MongoDB

## 🎯 Tổng quan

Thiết kế kiến trúc fullstack cho hệ thống e-commerce multi-role với frontend React 18 + Next.js 14 + ShadcnUI và backend Nest.js + MongoDB, tích hợp các thành phần có giá trị từ Medoo WebApp.

## 🏗️ System Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    CLIENT LAYER                             │
├─────────────────────────────────────────────────────────────┤
│  Buyer Portal    │  Seller Portal   │  Admin Portal        │
│  (Next.js 14)    │  (Next.js 14)    │  (Next.js 14)        │
│  - Product View  │  - Product Mgmt   │  - User Management   │
│  - Shopping Cart │  - Order Mgmt     │  - Platform Analytics│
│  - Checkout      │  - Analytics      │  - Content Moderation│
└─────────────────────────────────────────────────────────────┘
                              │
                    ┌─────────┴─────────┐
                    │   API Gateway     │
                    │   (Optional)      │
                    └─────────┬─────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   BACKEND LAYER                             │
├─────────────────────────────────────────────────────────────┤
│                    Nest.js API                              │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │   Auth      │  Products   │   Orders    │   Users     │  │
│  │  Module     │   Module    │   Module    │   Module    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Payments    │ Analytics   │ Notifications│ File Upload │  │
│  │  Module     │   Module    │   Module    │   Module    │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  DATABASE LAYER                             │
├─────────────────────────────────────────────────────────────┤
│                     MongoDB                                 │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │    Users    │  Products   │   Orders    │  Categories │  │
│  │ Collection  │ Collection  │ Collection  │ Collection  │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │   Reviews   │  Analytics  │    Logs     │   Sessions  │  │
│  │ Collection  │ Collection  │ Collection  │ Collection  │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 🎨 Frontend Architecture (React 18 + Next.js 14)

### **Project Structure**
```
frontend/
├── src/
│   ├── app/                           # Next.js 14 App Router
│   │   ├── (buyer)/                   # Buyer Portal Routes
│   │   │   ├── layout.tsx            # Buyer-specific layout
│   │   │   ├── page.tsx              # Homepage
│   │   │   ├── products/             # Product catalog
│   │   │   │   ├── page.tsx          # Product listing
│   │   │   │   ├── [id]/page.tsx     # Product detail
│   │   │   │   └── category/[slug]/  # Category pages
│   │   │   ├── cart/                 # Shopping cart
│   │   │   ├── checkout/             # Checkout flow
│   │   │   ├── orders/               # Order history
│   │   │   └── profile/              # User profile
│   │   ├── (seller)/                 # Seller Portal Routes
│   │   │   ├── layout.tsx            # Seller-specific layout
│   │   │   ├── dashboard/            # Seller dashboard
│   │   │   ├── products/             # Product management
│   │   │   │   ├── page.tsx          # Product list
│   │   │   │   ├── create/           # Create product
│   │   │   │   └── [id]/edit/        # Edit product
│   │   │   ├── orders/               # Order management
│   │   │   ├── analytics/            # Sales analytics
│   │   │   └── settings/             # Seller settings
│   │   ├── (admin)/                  # Admin Portal Routes
│   │   │   ├── layout.tsx            # Admin-specific layout
│   │   │   ├── dashboard/            # Admin dashboard
│   │   │   ├── users/                # User management
│   │   │   ├── sellers/              # Seller management
│   │   │   ├── products/             # Product moderation
│   │   │   ├── orders/               # Order oversight
│   │   │   └── analytics/            # Platform analytics
│   │   ├── auth/                     # Auth pages
│   │   │   ├── login/                # Login page
│   │   │   ├── register/             # Registration
│   │   │   └── forgot-password/      # Password reset
│   │   ├── globals.css               # Global styles
│   │   ├── layout.tsx                # Root layout
│   │   └── middleware.ts             # Route protection
│   ├── components/
│   │   ├── ui/                       # ShadcnUI components
│   │   ├── buyer/                    # Buyer-specific components
│   │   │   ├── product-card/
│   │   │   ├── cart-drawer/
│   │   │   ├── checkout-form/
│   │   │   └── order-tracking/
│   │   ├── seller/                   # Seller-specific components
│   │   │   ├── product-form/
│   │   │   ├── order-table/
│   │   │   ├── analytics-chart/
│   │   │   └── inventory-manager/
│   │   ├── admin/                    # Admin-specific components
│   │   │   ├── user-table/
│   │   │   ├── moderation-panel/
│   │   │   ├── system-metrics/
│   │   │   └── platform-analytics/
│   │   └── shared/                   # Shared components
│   │       ├── layout/
│   │       ├── forms/                # Adapted from Medoo
│   │       ├── auth/
│   │       └── common/
│   ├── lib/
│   │   ├── api/                      # API clients for Nest.js backend
│   │   │   ├── auth.api.ts
│   │   │   ├── products.api.ts
│   │   │   ├── orders.api.ts
│   │   │   └── users.api.ts
│   │   ├── stores/                   # Zustand stores
│   │   │   ├── auth.store.ts
│   │   │   ├── cart.store.ts
│   │   │   ├── products.store.ts
│   │   │   └── ui.store.ts
│   │   ├── hooks/                    # Custom hooks
│   │   │   ├── use-auth.ts
│   │   │   ├── use-cart.ts
│   │   │   └── use-api.ts
│   │   ├── utils/                    # Utilities (from Medoo)
│   │   │   ├── auth-utils.ts
│   │   │   ├── format-utils.ts
│   │   │   └── validation-utils.ts
│   │   ├── form-builder/             # Schema-form (adapted for ShadcnUI)
│   │   │   ├── form-builder.tsx
│   │   │   ├── field-types.tsx
│   │   │   └── validation.ts
│   │   └── constants/
│   │       ├── api-endpoints.ts
│   │       └── app-config.ts
│   ├── types/                        # TypeScript types
│   │   ├── auth.types.ts
│   │   ├── product.types.ts
│   │   ├── order.types.ts
│   │   └── api.types.ts
│   └── styles/                       # Styling files
│       ├── globals.css
│       └── components.css
├── public/                           # Static assets
├── package.json
├── next.config.js
├── tailwind.config.js
└── tsconfig.json
```

### **Technology Stack - Frontend**
```json
{
  "framework": "Next.js 14 (App Router)",
  "react": "React 18",
  "ui": "ShadcnUI + Tailwind CSS",
  "state": "Zustand + React Query",
  "forms": "React Hook Form + Zod + Adapted Schema-form",
  "auth": "JWT + Custom auth hooks",
  "http": "Axios + React Query",
  "styling": "Tailwind CSS + CSS Modules",
  "icons": "Lucide React",
  "charts": "Recharts",
  "payments": "Stripe Elements"
}
```

## 🚀 Backend Architecture (Nest.js + MongoDB)

### **Project Structure**
```
backend/
├── src/
│   ├── app.module.ts                 # Root module
│   ├── main.ts                       # Application entry point
│   ├── auth/                         # Authentication module
│   │   ├── auth.module.ts
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   ├── guards/
│   │   │   ├── jwt-auth.guard.ts
│   │   │   └── roles.guard.ts
│   │   ├── strategies/
│   │   │   ├── jwt.strategy.ts
│   │   │   └── local.strategy.ts
│   │   └── dto/
│   │       ├── login.dto.ts
│   │       └── register.dto.ts
│   ├── users/                        # Users module
│   │   ├── users.module.ts
│   │   ├── users.controller.ts
│   │   ├── users.service.ts
│   │   ├── schemas/
│   │   │   └── user.schema.ts
│   │   └── dto/
│   │       ├── create-user.dto.ts
│   │       └── update-user.dto.ts
│   ├── products/                     # Products module
│   │   ├── products.module.ts
│   │   ├── products.controller.ts
│   │   ├── products.service.ts
│   │   ├── schemas/
│   │   │   └── product.schema.ts
│   │   └── dto/
│   │       ├── create-product.dto.ts
│   │       └── update-product.dto.ts
│   ├── orders/                       # Orders module
│   │   ├── orders.module.ts
│   │   ├── orders.controller.ts
│   │   ├── orders.service.ts
│   │   ├── schemas/
│   │   │   └── order.schema.ts
│   │   └── dto/
│   │       ├── create-order.dto.ts
│   │       └── update-order.dto.ts
│   ├── categories/                   # Categories module
│   │   ├── categories.module.ts
│   │   ├── categories.controller.ts
│   │   ├── categories.service.ts
│   │   └── schemas/
│   │       └── category.schema.ts
│   ├── payments/                     # Payments module
│   │   ├── payments.module.ts
│   │   ├── payments.controller.ts
│   │   ├── payments.service.ts
│   │   └── dto/
│   │       └── payment.dto.ts
│   ├── analytics/                    # Analytics module
│   │   ├── analytics.module.ts
│   │   ├── analytics.controller.ts
│   │   ├── analytics.service.ts
│   │   └── schemas/
│   │       └── analytics.schema.ts
│   ├── notifications/                # Notifications module
│   │   ├── notifications.module.ts
│   │   ├── notifications.controller.ts
│   │   ├── notifications.service.ts
│   │   └── schemas/
│   │       └── notification.schema.ts
│   ├── file-upload/                  # File upload module
│   │   ├── file-upload.module.ts
│   │   ├── file-upload.controller.ts
│   │   └── file-upload.service.ts
│   ├── common/                       # Common utilities
│   │   ├── decorators/
│   │   │   ├── roles.decorator.ts
│   │   │   └── user.decorator.ts
│   │   ├── filters/
│   │   │   └── http-exception.filter.ts
│   │   ├── interceptors/
│   │   │   └── transform.interceptor.ts
│   │   ├── pipes/
│   │   │   └── validation.pipe.ts
│   │   └── constants/
│   │       └── roles.constant.ts
│   └── config/                       # Configuration
│       ├── database.config.ts
│       ├── jwt.config.ts
│       └── app.config.ts
├── test/                             # Tests
├── package.json
├── nest-cli.json
└── tsconfig.json
```

### **Technology Stack - Backend**
```json
{
  "framework": "Nest.js",
  "database": "MongoDB + Mongoose",
  "auth": "JWT + Passport",
  "validation": "Class Validator + Class Transformer",
  "documentation": "Swagger/OpenAPI",
  "testing": "Jest + Supertest",
  "file_upload": "Multer + AWS S3/Cloudinary",
  "payments": "Stripe",
  "email": "NodeMailer + SendGrid",
  "caching": "Redis (optional)",
  "logging": "Winston"
}
```

## 🗄️ Database Design (MongoDB)

### **Collections Schema**
```typescript
// User Collection
interface User {
  _id: ObjectId;
  email: string;
  password: string; // hashed
  firstName: string;
  lastName: string;
  avatar?: string;
  roles: ('buyer' | 'seller' | 'admin')[];
  isActive: boolean;
  emailVerified: boolean;
  phone?: string;
  addresses: Address[];
  createdAt: Date;
  updatedAt: Date;
}

// Product Collection
interface Product {
  _id: ObjectId;
  name: string;
  description: string;
  shortDescription: string;
  price: number;
  comparePrice?: number;
  cost?: number;
  sku: string;
  barcode?: string;
  trackQuantity: boolean;
  quantity: number;
  images: string[];
  category: ObjectId; // ref to Category
  seller: ObjectId; // ref to User
  status: 'draft' | 'active' | 'archived';
  tags: string[];
  variants: ProductVariant[];
  seo: {
    title: string;
    description: string;
    slug: string;
  };
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Order Collection
interface Order {
  _id: ObjectId;
  orderNumber: string;
  buyer: ObjectId; // ref to User
  items: OrderItem[];
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod: 'stripe' | 'paypal' | 'cod';
  paymentId?: string;
  shippingAddress: Address;
  billingAddress: Address;
  notes?: string;
  trackingNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Category Collection
interface Category {
  _id: ObjectId;
  name: string;
  description?: string;
  slug: string;
  image?: string;
  parent?: ObjectId; // ref to Category
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### **Indexes Strategy**
```javascript
// User indexes
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ roles: 1 });
db.users.createIndex({ isActive: 1 });

// Product indexes
db.products.createIndex({ seller: 1 });
db.products.createIndex({ category: 1 });
db.products.createIndex({ status: 1 });
db.products.createIndex({ "seo.slug": 1 }, { unique: true });
db.products.createIndex({ name: "text", description: "text" });
db.products.createIndex({ tags: 1 });
db.products.createIndex({ price: 1 });

// Order indexes
db.orders.createIndex({ buyer: 1 });
db.orders.createIndex({ orderNumber: 1 }, { unique: true });
db.orders.createIndex({ status: 1 });
db.orders.createIndex({ createdAt: -1 });
db.orders.createIndex({ "items.product": 1 });

// Category indexes
db.categories.createIndex({ slug: 1 }, { unique: true });
db.categories.createIndex({ parent: 1 });
db.categories.createIndex({ sortOrder: 1 });
```

## 🔌 API Design & Endpoints

### **Authentication Endpoints**
```typescript
// auth.controller.ts
@Controller('auth')
export class AuthController {
  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    // Register new user
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto) {
    // Login user and return JWT
  }

  @Post('refresh')
  async refresh(@Body() refreshDto: RefreshTokenDto) {
    // Refresh JWT token
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  async logout(@User() user: UserDocument) {
    // Logout user
  }

  @Post('forgot-password')
  async forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    // Send password reset email
  }

  @Post('reset-password')
  async resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    // Reset password with token
  }
}
```

### **Products Endpoints**
```typescript
// products.controller.ts
@Controller('products')
export class ProductsController {
  // Public endpoints
  @Get()
  async findAll(@Query() query: ProductQueryDto) {
    // Get products with filtering, sorting, pagination
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    // Get single product
  }

  @Get('category/:categoryId')
  async findByCategory(@Param('categoryId') categoryId: string) {
    // Get products by category
  }

  @Get('search')
  async search(@Query('q') query: string) {
    // Search products
  }

  // Seller endpoints
  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('seller')
  async create(@Body() createProductDto: CreateProductDto, @User() user: UserDocument) {
    // Create new product
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('seller')
  async update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto, @User() user: UserDocument) {
    // Update product (only owner)
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('seller')
  async remove(@Param('id') id: string, @User() user: UserDocument) {
    // Delete product (only owner)
  }

  // Admin endpoints
  @Get('admin/all')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async findAllForAdmin(@Query() query: AdminProductQueryDto) {
    // Get all products for admin
  }

  @Put('admin/:id/approve')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async approve(@Param('id') id: string) {
    // Approve product
  }
}
```

### **Orders Endpoints**
```typescript
// orders.controller.ts
@Controller('orders')
export class OrdersController {
  // Buyer endpoints
  @Post()
  @UseGuards(JwtAuthGuard)
  async create(@Body() createOrderDto: CreateOrderDto, @User() user: UserDocument) {
    // Create new order
  }

  @Get('my-orders')
  @UseGuards(JwtAuthGuard)
  async getMyOrders(@User() user: UserDocument, @Query() query: OrderQueryDto) {
    // Get user's orders
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: string, @User() user: UserDocument) {
    // Get single order (buyer or seller)
  }

  @Put(':id/cancel')
  @UseGuards(JwtAuthGuard)
  async cancel(@Param('id') id: string, @User() user: UserDocument) {
    // Cancel order (buyer only)
  }

  // Seller endpoints
  @Get('seller/orders')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('seller')
  async getSellerOrders(@User() user: UserDocument, @Query() query: OrderQueryDto) {
    // Get orders for seller's products
  }

  @Put(':id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('seller')
  async updateStatus(@Param('id') id: string, @Body() updateStatusDto: UpdateOrderStatusDto, @User() user: UserDocument) {
    // Update order status
  }

  // Admin endpoints
  @Get('admin/all')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async getAllOrders(@Query() query: AdminOrderQueryDto) {
    // Get all orders for admin
  }
}
```

## 🔗 Frontend-Backend Integration

### **API Client Setup**
```typescript
// lib/api/client.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { useAuthStore } from '@/lib/stores/auth.store';

class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api',
      timeout: 10000,
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = useAuthStore.getState().token;
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired, try to refresh
          const refreshToken = useAuthStore.getState().refreshToken;
          if (refreshToken) {
            try {
              const response = await this.client.post('/auth/refresh', {
                refreshToken,
              });
              const { token } = response.data;
              useAuthStore.getState().setToken(token);

              // Retry original request
              error.config.headers.Authorization = `Bearer ${token}`;
              return this.client.request(error.config);
            } catch (refreshError) {
              // Refresh failed, logout user
              useAuthStore.getState().logout();
              window.location.href = '/auth/login';
            }
          } else {
            useAuthStore.getState().logout();
            window.location.href = '/auth/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.client.delete(url, config);
    return response.data;
  }
}

export const apiClient = new ApiClient();
```

### **React Query Integration**
```typescript
// lib/api/queries/products.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '../client';
import { Product, ProductQueryParams, CreateProductDto } from '@/types';

// Query hooks
export const useProducts = (params?: ProductQueryParams) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => apiClient.get<{ data: Product[]; total: number }>('/products', { params }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProduct = (id: string) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => apiClient.get<Product>(`/products/${id}`),
    enabled: !!id,
  });
};

export const useSellerProducts = (params?: ProductQueryParams) => {
  return useQuery({
    queryKey: ['seller-products', params],
    queryFn: () => apiClient.get<{ data: Product[]; total: number }>('/products/seller/my-products', { params }),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Mutation hooks
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductDto) => apiClient.post<Product>('/products', data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['seller-products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateProductDto> }) =>
      apiClient.put<Product>(`/products/${id}`, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['product', id] });
      queryClient.invalidateQueries({ queryKey: ['seller-products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.delete(`/products/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['seller-products'] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};
```

## 🔄 State Management với Zustand

### **Auth Store**
```typescript
// lib/stores/auth.store.ts
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { apiClient } from '@/lib/api/client';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  roles: ('buyer' | 'seller' | 'admin')[];
}

interface AuthStore {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;

  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  setToken: (token: string) => void;
  setUser: (user: User) => void;
  hasRole: (role: string) => boolean;
  refreshAuth: () => Promise<void>;
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      refreshToken: null,
      isAuthenticated: false,

      login: async (email, password) => {
        try {
          const response = await apiClient.post<{
            user: User;
            token: string;
            refreshToken: string;
          }>('/auth/login', { email, password });

          const { user, token, refreshToken } = response;

          set({
            user,
            token,
            refreshToken,
            isAuthenticated: true,
          });
        } catch (error) {
          throw error;
        }
      },

      register: async (userData) => {
        try {
          const response = await apiClient.post<{
            user: User;
            token: string;
            refreshToken: string;
          }>('/auth/register', userData);

          const { user, token, refreshToken } = response;

          set({
            user,
            token,
            refreshToken,
            isAuthenticated: true,
          });
        } catch (error) {
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          refreshToken: null,
          isAuthenticated: false,
        });
      },

      setToken: (token) => {
        set({ token });
      },

      setUser: (user) => {
        set({ user, isAuthenticated: true });
      },

      hasRole: (role) => {
        const { user } = get();
        return user?.roles.includes(role as any) || false;
      },

      refreshAuth: async () => {
        try {
          const { refreshToken } = get();
          if (!refreshToken) throw new Error('No refresh token');

          const response = await apiClient.post<{
            token: string;
            refreshToken: string;
          }>('/auth/refresh', { refreshToken });

          set({
            token: response.token,
            refreshToken: response.refreshToken,
          });
        } catch (error) {
          get().logout();
          throw error;
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
```

### **Cart Store**
```typescript
// lib/stores/cart.store.ts
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  variant?: {
    size?: string;
    color?: string;
  };
  seller: {
    id: string;
    name: string;
  };
}

interface CartStore {
  items: CartItem[];
  total: number;
  itemCount: number;

  // Actions
  addItem: (product: any, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  getItemById: (productId: string) => CartItem | undefined;
  calculateTotal: () => void;
  getItemsBySeller: () => Record<string, CartItem[]>;
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      total: 0,
      itemCount: 0,

      addItem: (product, quantity = 1) => {
        const { items } = get();
        const existingItem = items.find(item => item.productId === product.id);

        if (existingItem) {
          set({
            items: items.map(item =>
              item.productId === product.id
                ? { ...item, quantity: item.quantity + quantity }
                : item
            )
          });
        } else {
          const newItem: CartItem = {
            id: `${product.id}-${Date.now()}`,
            productId: product.id,
            name: product.name,
            price: product.price,
            quantity,
            image: product.images[0] || '',
            seller: {
              id: product.seller.id,
              name: product.seller.name,
            },
          };

          set({
            items: [...items, newItem]
          });
        }

        get().calculateTotal();
      },

      removeItem: (productId) => {
        set({
          items: get().items.filter(item => item.productId !== productId)
        });
        get().calculateTotal();
      },

      updateQuantity: (productId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(productId);
          return;
        }

        set({
          items: get().items.map(item =>
            item.productId === productId ? { ...item, quantity } : item
          )
        });
        get().calculateTotal();
      },

      clearCart: () => {
        set({ items: [], total: 0, itemCount: 0 });
      },

      getItemById: (productId) => {
        return get().items.find(item => item.productId === productId);
      },

      calculateTotal: () => {
        const { items } = get();
        const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
        set({ total, itemCount });
      },

      getItemsBySeller: () => {
        const { items } = get();
        return items.reduce((acc, item) => {
          const sellerId = item.seller.id;
          if (!acc[sellerId]) {
            acc[sellerId] = [];
          }
          acc[sellerId].push(item);
          return acc;
        }, {} as Record<string, CartItem[]>);
      },
    }),
    {
      name: 'cart-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
```

## 🔐 Authentication Flow

### **Middleware Protection**
```typescript
// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';

const PUBLIC_ROUTES = ['/', '/products', '/auth', '/api/auth'];
const ROLE_ROUTES = {
  buyer: ['/cart', '/checkout', '/orders', '/profile'],
  seller: ['/seller'],
  admin: ['/admin'],
} as const;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public routes
  if (PUBLIC_ROUTES.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }

  // Get token from cookie or header
  const token = request.cookies.get('auth-token')?.value ||
                request.headers.get('authorization')?.replace('Bearer ', '');

  if (!token) {
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  try {
    // Verify JWT token
    const secret = new TextEncoder().encode(process.env.JWT_SECRET);
    const { payload } = await jwtVerify(token, secret);

    const userRoles = payload.roles as string[];

    // Check role-based access
    for (const [role, routes] of Object.entries(ROLE_ROUTES)) {
      if (routes.some(route => pathname.startsWith(route))) {
        if (!userRoles.includes(role)) {
          return NextResponse.redirect(new URL('/unauthorized', request.url));
        }
      }
    }

    // Add user info to headers for API routes
    const response = NextResponse.next();
    response.headers.set('x-user-id', payload.sub as string);
    response.headers.set('x-user-roles', userRoles.join(','));

    return response;
  } catch (error) {
    // Invalid token
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
```

### **Auth Hooks**
```typescript
// lib/hooks/use-auth.ts
import { useAuthStore } from '@/lib/stores/auth.store';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export const useAuth = () => {
  const {
    user,
    isAuthenticated,
    login,
    register,
    logout,
    hasRole,
  } = useAuthStore();

  return {
    user,
    isAuthenticated,
    login,
    register,
    logout,
    hasRole,
  };
};

export const useRequireAuth = (redirectTo = '/auth/login') => {
  const { isAuthenticated } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push(redirectTo);
    }
  }, [isAuthenticated, redirectTo, router]);

  return isAuthenticated;
};

export const useRequireRole = (role: string, redirectTo = '/unauthorized') => {
  const { hasRole, isAuthenticated } = useAuthStore();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated && !hasRole(role)) {
      router.push(redirectTo);
    }
  }, [hasRole, role, redirectTo, router, isAuthenticated]);

  return hasRole(role);
};
```

## 🛠️ Implementation Guide

### **Phase 1: Backend Setup (Week 1-2)**
```bash
# 1. Create Nest.js project
npm i -g @nestjs/cli
nest new ecommerce-backend
cd ecommerce-backend

# 2. Install dependencies
npm install @nestjs/mongoose mongoose
npm install @nestjs/jwt @nestjs/passport passport passport-jwt
npm install @nestjs/config class-validator class-transformer
npm install bcryptjs stripe nodemailer

# 3. Setup MongoDB connection
# Add to app.module.ts
MongooseModule.forRoot(process.env.MONGODB_URI)

# 4. Generate modules
nest generate module auth
nest generate module users
nest generate module products
nest generate module orders
nest generate module categories
```

### **Phase 2: Frontend Setup (Week 2-3)**
```bash
# 1. Create Next.js project
npx create-next-app@latest ecommerce-frontend --typescript --tailwind --app
cd ecommerce-frontend

# 2. Install dependencies
npm install @tanstack/react-query zustand axios
npm install @hookform/react-hook-form zod @hookform/resolvers
npm install lucide-react clsx tailwind-merge

# 3. Setup ShadcnUI
npx shadcn-ui@latest init
npx shadcn-ui@latest add button input select card form table

# 4. Extract Medoo components
mkdir -p src/lib/extracted-from-medoo
# Copy utilities, form-builder, auth patterns
```

### **Phase 3: Core Features (Week 4-8)**
```typescript
// 5. Implement authentication
// Backend: JWT strategy, guards, decorators
// Frontend: Auth store, hooks, middleware

// 6. Product management
// Backend: CRUD operations, search, filtering
// Frontend: Product catalog, seller dashboard

// 7. Order system
// Backend: Order creation, status management
// Frontend: Cart, checkout, order tracking

// 8. User management
// Backend: User profiles, role management
// Frontend: User dashboard, admin panel
```

### **Phase 4: Advanced Features (Week 9-12)**
```typescript
// 9. Payment integration
// Stripe integration for payments
// Order confirmation and tracking

// 10. File upload
// Image upload for products
// AWS S3 or Cloudinary integration

// 11. Analytics
// Sales analytics for sellers
// Platform analytics for admin

// 12. Notifications
// Email notifications
// In-app notifications
```

## 🚀 Deployment Strategy

### **Backend Deployment (Nest.js)**
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "run", "start:prod"]
```

### **Frontend Deployment (Next.js)**
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### **Docker Compose**
```yaml
# docker-compose.yml
version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - MONGODB_URI=*****************************************************************
      - JWT_SECRET=your-jwt-secret
      - STRIPE_SECRET_KEY=your-stripe-secret
    depends_on:
      - mongodb

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3001/api
    depends_on:
      - backend

volumes:
  mongodb_data:
```

## 📊 Performance & Optimization

### **Backend Optimization**
```typescript
// Enable compression
app.use(compression());

// Rate limiting
app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
}));

// Caching with Redis (optional)
@Injectable()
export class CacheService {
  constructor(@Inject('REDIS_CLIENT') private redis: Redis) {}

  async get(key: string): Promise<string | null> {
    return this.redis.get(key);
  }

  async set(key: string, value: string, ttl: number): Promise<void> {
    await this.redis.setex(key, ttl, value);
  }
}
```

### **Frontend Optimization**
```typescript
// Image optimization
import Image from 'next/image';

export const ProductImage = ({ src, alt, ...props }) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={400}
      height={400}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,..."
      {...props}
    />
  );
};

// Dynamic imports
const AdminPanel = dynamic(() => import('./AdminPanel'), {
  loading: () => <AdminPanelSkeleton />,
  ssr: false
});
```

## 🎯 Success Metrics

### **Technical KPIs**
- **API Response Time**: < 200ms average
- **Database Query Time**: < 50ms average
- **Frontend Load Time**: < 2s first load
- **Bundle Size**: < 1MB per portal
- **Test Coverage**: > 80%

### **Business KPIs**
- **Development Speed**: 50% faster với extracted components
- **Bug Rate**: < 1% với TypeScript + validation
- **User Satisfaction**: > 4.5/5
- **Conversion Rate**: Target +25% improvement

---

## 📋 **Tóm tắt & Khuyến nghị**

### **✅ Architecture Strengths:**
- **Separation of Concerns**: Frontend và backend tách biệt rõ ràng
- **Scalability**: Microservices-ready architecture
- **Type Safety**: Full TypeScript coverage
- **Modern Stack**: React 18 + Next.js 14 + Nest.js + MongoDB

### **💰 Expected Benefits:**
- **Development Time**: 40-50% faster với extracted components từ Medoo
- **Maintenance**: Easier với clean architecture
- **Performance**: Better với optimized stack
- **Scalability**: Ready cho future growth

### **🎯 Final Recommendation:**
**PROCEED** với React 18 + Next.js 14 + Nest.js + MongoDB stack. Đây là lựa chọn an toàn và proven, với khả năng tích hợp tốt các components từ Medoo.

**Timeline**: 12-16 weeks với team 3-4 developers
**Budget Impact**: 40-50% savings nhờ component reuse
