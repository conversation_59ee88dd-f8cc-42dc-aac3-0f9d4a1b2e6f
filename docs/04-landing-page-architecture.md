# Landing Page Architecture - Vite + React Application

## 🎯 Tổng quan

Landing Page là ứng dụng marketing được xây dựng với Vite và React 18, tập trung vào Web3 integration và lead generation. Ứng dụng được tối ưu cho performance và SEO.

## 📁 Cấu trúc Th<PERSON> mục

```
packages/landing-page/
├── src/
│   ├── components/        # React components
│   │   ├── ui/           # UI components
│   │   ├── forms/        # Form components
│   │   └── sections/     # Page sections
│   ├── dapp/             # Web3/DApp integration
│   │   ├── providers/    # Web3 providers
│   │   ├── hooks/        # Web3 hooks
│   │   └── utils/        # Web3 utilities
│   ├── lib/              # Utility libraries
│   │   ├── providers/    # Context providers
│   │   ├── hook/         # Custom hooks
│   │   └── utils/        # Utility functions
│   ├── pages/            # Page components
│   ├── store/            # Redux store
│   ├── styles/           # Styling files
│   ├── App.tsx           # Main app component
│   ├── main.tsx          # Entry point
│   ├── i18n.ts           # Internationalization
│   └── index.css         # Global styles
├── public/               # Static assets
├── vite.config.ts        # Vite configuration
├── tailwind.config.js    # Tailwind configuration
└── package.json
```

## ⚙️ Vite Configuration

### vite.config.ts
```typescript
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const isDevelopment = mode === "development";
  
  const plugins = [
    react(),
    tsconfigPaths(),
  ];

  // Node.js polyfills for Web3 compatibility
  plugins.push(nodePolyfills({
    globals: {
      Buffer: true,
      process: true
    }
  }));

  return {
    plugins,
    define: {
      global: "globalThis",
    },
    resolve: {
      alias: {
        src: path.resolve(__dirname, "./src"),
        "@": path.resolve(__dirname, "./src"),
      },
    },
    build: {
      target: "esnext",
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["react", "react-dom"],
            antd: ["antd"],
            web3: ["wagmi", "viem", "connectkit"],
            redux: ["@reduxjs/toolkit", "react-redux"],
          },
        },
      },
    },
    optimizeDeps: {
      include: [
        "react",
        "react-dom",
        "antd",
        "wagmi",
        "viem",
        "@reduxjs/toolkit",
      ],
    },
  };
});
```

## 🔄 State Management với Redux Toolkit

### Store Configuration
```typescript
// store/index.ts
import { configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import authSlice from "./slices/authSlice";
import campaignSlice from "./slices/campaignSlice";
import web3Slice from "./slices/web3Slice";

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["auth", "web3"], // Only persist auth and web3 state
};

const rootReducer = {
  auth: authSlice,
  campaign: campaignSlice,
  web3: web3Slice,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
      },
    }),
});

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

### Auth Slice
```typescript
// store/slices/authSlice.ts
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";

interface AuthState {
  user: any | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  loading: false,
  error: null,
};

// Async thunks
export const loginUser = createAsyncThunk(
  "auth/loginUser",
  async (credentials: { email: string; password: string }) => {
    const response = await fetch("/api/auth/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(credentials),
    });
    
    if (!response.ok) {
      throw new Error("Login failed");
    }
    
    return response.json();
  }
);

export const registerUser = createAsyncThunk(
  "auth/registerUser",
  async (userData: { email: string; password: string; name: string }) => {
    const response = await fetch("/api/auth/register", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(userData),
    });
    
    if (!response.ok) {
      throw new Error("Registration failed");
    }
    
    return response.json();
  }
);

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Login failed";
      })
      // Register
      .addCase(registerUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.loading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Registration failed";
      });
  },
});

export const { logout, clearError } = authSlice.actions;
export default authSlice.reducer;
```

## 🌐 Web3 Integration với Wagmi

### Web3 Provider Setup
```typescript
// dapp/providers/evm/index.tsx
import { WagmiProvider, createConfig, http } from "wagmi";
import { mainnet, polygon, arbitrum, base } from "wagmi/chains";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ConnectKitProvider, getDefaultConfig } from "connectkit";

const config = createConfig(
  getDefaultConfig({
    chains: [mainnet, polygon, arbitrum, base],
    transports: {
      [mainnet.id]: http(),
      [polygon.id]: http(),
      [arbitrum.id]: http(),
      [base.id]: http(),
    },
    walletConnectProjectId: process.env.VITE_WALLETCONNECT_PROJECT_ID!,
    appName: "Medoo Landing Page",
    appDescription: "Medoo Web3 Landing Page",
    appUrl: "https://medoo.io",
    appIcon: "https://medoo.io/logo.png",
  })
);

const queryClient = new QueryClient();

export const Web3Provider = ({ children }: { children: React.ReactNode }) => {
  return (
    <WagmiProvider config={config}>
      <QueryClientProvider client={queryClient}>
        <ConnectKitProvider
          theme="midnight"
          mode="dark"
          customTheme={{
            "--ck-accent-color": "#2F57EF",
            "--ck-accent-text-color": "#ffffff",
          }}
        >
          {children}
        </ConnectKitProvider>
      </QueryClientProvider>
    </WagmiProvider>
  );
};
```

### Web3 Hooks
```typescript
// dapp/hooks/useWeb3.ts
import { useAccount, useBalance, useDisconnect } from "wagmi";
import { useConnectModal } from "connectkit";
import { useAppDispatch, useAppSelector } from "src/store/hooks";
import { setWalletConnected, setWalletAddress } from "src/store/slices/web3Slice";

export const useWeb3Connection = () => {
  const dispatch = useAppDispatch();
  const { isConnected, address, chain } = useAccount();
  const { data: balance } = useBalance({ address });
  const { disconnect } = useDisconnect();
  const { setOpen } = useConnectModal();
  
  const { isWalletConnected, walletAddress } = useAppSelector(
    (state) => state.web3
  );

  // Sync wagmi state with Redux
  useEffect(() => {
    if (isConnected && address) {
      dispatch(setWalletConnected(true));
      dispatch(setWalletAddress(address));
    } else {
      dispatch(setWalletConnected(false));
      dispatch(setWalletAddress(null));
    }
  }, [isConnected, address, dispatch]);

  const connectWallet = () => {
    setOpen(true);
  };

  const disconnectWallet = () => {
    disconnect();
  };

  return {
    isConnected,
    address,
    chain,
    balance,
    connectWallet,
    disconnectWallet,
  };
};
```

### Token Staking Hook
```typescript
// dapp/hooks/useStaking.ts
import { useWriteContract, useReadContract, useWaitForTransactionReceipt } from "wagmi";
import { parseEther, formatEther } from "viem";
import { STAKING_CONTRACT_ABI, STAKING_CONTRACT_ADDRESS } from "../constants";

export const useStaking = () => {
  const { writeContract, data: hash, isPending } = useWriteContract();
  
  const { isLoading: isConfirming, isSuccess: isConfirmed } =
    useWaitForTransactionReceipt({ hash });

  // Read staked amount
  const { data: stakedAmount } = useReadContract({
    address: STAKING_CONTRACT_ADDRESS,
    abi: STAKING_CONTRACT_ABI,
    functionName: "getStakedAmount",
    args: [address],
  });

  // Read rewards
  const { data: pendingRewards } = useReadContract({
    address: STAKING_CONTRACT_ADDRESS,
    abi: STAKING_CONTRACT_ABI,
    functionName: "getPendingRewards",
    args: [address],
  });

  const stakeTokens = async (amount: string) => {
    try {
      await writeContract({
        address: STAKING_CONTRACT_ADDRESS,
        abi: STAKING_CONTRACT_ABI,
        functionName: "stake",
        args: [parseEther(amount)],
      });
    } catch (error) {
      console.error("Staking failed:", error);
      throw error;
    }
  };

  const unstakeTokens = async (amount: string) => {
    try {
      await writeContract({
        address: STAKING_CONTRACT_ADDRESS,
        abi: STAKING_CONTRACT_ABI,
        functionName: "unstake",
        args: [parseEther(amount)],
      });
    } catch (error) {
      console.error("Unstaking failed:", error);
      throw error;
    }
  };

  const claimRewards = async () => {
    try {
      await writeContract({
        address: STAKING_CONTRACT_ADDRESS,
        abi: STAKING_CONTRACT_ABI,
        functionName: "claimRewards",
      });
    } catch (error) {
      console.error("Claiming rewards failed:", error);
      throw error;
    }
  };

  return {
    stakeTokens,
    unstakeTokens,
    claimRewards,
    stakedAmount: stakedAmount ? formatEther(stakedAmount) : "0",
    pendingRewards: pendingRewards ? formatEther(pendingRewards) : "0",
    isPending,
    isConfirming,
    isConfirmed,
  };
};
```

## 🎨 UI Components với Ant Design

### Landing Page Sections
```typescript
// components/sections/HeroSection.tsx
import { Button, Typography, Space } from "antd";
import { ConnectKitButton } from "connectkit";
import { useWeb3Connection } from "src/dapp/hooks/useWeb3";

const { Title, Paragraph } = Typography;

export const HeroSection = () => {
  const { isConnected, connectWallet } = useWeb3Connection();

  return (
    <section className="hero-section">
      <div className="container mx-auto px-4 py-20">
        <div className="text-center max-w-4xl mx-auto">
          <Title level={1} className="hero-title">
            The Future of
            <span className="gradient-text"> Decentralized Learning</span>
          </Title>
          
          <Paragraph className="hero-description text-xl mb-8">
            Join the revolution in education with blockchain-powered learning,
            NFT certificates, and token rewards for your achievements.
          </Paragraph>
          
          <Space size="large" className="hero-actions">
            {isConnected ? (
              <ConnectKitButton />
            ) : (
              <Button
                type="primary"
                size="large"
                onClick={connectWallet}
                className="connect-wallet-btn"
              >
                Connect Wallet to Start
              </Button>
            )}
            
            <Button size="large" ghost>
              Learn More
            </Button>
          </Space>
        </div>
      </div>
    </section>
  );
};
```

### Staking Component
```typescript
// components/sections/StakingSection.tsx
import { useState } from "react";
import { Card, Input, Button, Statistic, Row, Col, message } from "antd";
import { useStaking } from "src/dapp/hooks/useStaking";
import { useWeb3Connection } from "src/dapp/hooks/useWeb3";

export const StakingSection = () => {
  const [stakeAmount, setStakeAmount] = useState("");
  const [unstakeAmount, setUnstakeAmount] = useState("");
  
  const { isConnected } = useWeb3Connection();
  const {
    stakeTokens,
    unstakeTokens,
    claimRewards,
    stakedAmount,
    pendingRewards,
    isPending,
    isConfirming,
  } = useStaking();

  const handleStake = async () => {
    if (!stakeAmount || parseFloat(stakeAmount) <= 0) {
      message.error("Please enter a valid amount");
      return;
    }

    try {
      await stakeTokens(stakeAmount);
      message.success("Staking transaction submitted!");
      setStakeAmount("");
    } catch (error) {
      message.error("Staking failed");
    }
  };

  const handleUnstake = async () => {
    if (!unstakeAmount || parseFloat(unstakeAmount) <= 0) {
      message.error("Please enter a valid amount");
      return;
    }

    try {
      await unstakeTokens(unstakeAmount);
      message.success("Unstaking transaction submitted!");
      setUnstakeAmount("");
    } catch (error) {
      message.error("Unstaking failed");
    }
  };

  const handleClaimRewards = async () => {
    try {
      await claimRewards();
      message.success("Rewards claimed successfully!");
    } catch (error) {
      message.error("Claiming rewards failed");
    }
  };

  if (!isConnected) {
    return (
      <Card className="staking-card">
        <div className="text-center">
          <p>Please connect your wallet to access staking features</p>
        </div>
      </Card>
    );
  }

  return (
    <section className="staking-section py-20">
      <div className="container mx-auto px-4">
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={8}>
            <Card title="Your Staking Stats" className="stats-card">
              <Statistic
                title="Staked Amount"
                value={stakedAmount}
                suffix="MEDOO"
                precision={4}
              />
              <Statistic
                title="Pending Rewards"
                value={pendingRewards}
                suffix="MEDOO"
                precision={4}
                className="mt-4"
              />
              <Button
                type="primary"
                block
                onClick={handleClaimRewards}
                loading={isPending || isConfirming}
                disabled={parseFloat(pendingRewards) <= 0}
                className="mt-4"
              >
                Claim Rewards
              </Button>
            </Card>
          </Col>
          
          <Col xs={24} lg={8}>
            <Card title="Stake Tokens" className="stake-card">
              <Input
                placeholder="Amount to stake"
                value={stakeAmount}
                onChange={(e) => setStakeAmount(e.target.value)}
                suffix="MEDOO"
                className="mb-4"
              />
              <Button
                type="primary"
                block
                onClick={handleStake}
                loading={isPending || isConfirming}
                disabled={!stakeAmount}
              >
                Stake Tokens
              </Button>
            </Card>
          </Col>
          
          <Col xs={24} lg={8}>
            <Card title="Unstake Tokens" className="unstake-card">
              <Input
                placeholder="Amount to unstake"
                value={unstakeAmount}
                onChange={(e) => setUnstakeAmount(e.target.value)}
                suffix="MEDOO"
                className="mb-4"
              />
              <Button
                type="default"
                block
                onClick={handleUnstake}
                loading={isPending || isConfirming}
                disabled={!unstakeAmount}
              >
                Unstake Tokens
              </Button>
            </Card>
          </Col>
        </Row>
      </div>
    </section>
  );
};
```

## 🎨 Styling với Tailwind CSS

### Custom Theme Configuration
```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: "#eff6ff",
          100: "#dbeafe", 
          500: "#2F57EF",
          600: "#2563eb",
          700: "#1d4ed8",
          900: "#1e3a8a",
        },
        secondary: {
          500: "#8b5cf6",
          600: "#7c3aed",
        },
        accent: {
          500: "#f59e0b",
          600: "#d97706",
        },
      },
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
        heading: ["Poppins", "system-ui", "sans-serif"],
      },
      animation: {
        "fade-in": "fadeIn 0.6s ease-out",
        "slide-up": "slideUp 0.8s ease-out",
        "gradient": "gradient 3s ease infinite",
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "hero-pattern": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
      },
    },
  },
  plugins: [
    require("@tailwindcss/typography"),
    require("@tailwindcss/forms"),
  ],
};
```

### Component Styles
```scss
// styles/components/hero.scss
.hero-section {
  @apply relative min-h-screen flex items-center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  &::before {
    content: "";
    @apply absolute inset-0 bg-black bg-opacity-20;
  }
  
  .hero-title {
    @apply text-5xl md:text-7xl font-bold text-white mb-6;
    
    .gradient-text {
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
  
  .hero-description {
    @apply text-white text-opacity-90 max-w-2xl mx-auto;
  }
  
  .connect-wallet-btn {
    @apply bg-gradient-to-r from-yellow-400 to-orange-500 border-none;
    @apply hover:from-yellow-500 hover:to-orange-600 transform hover:scale-105;
    @apply transition-all duration-300;
  }
}

.staking-section {
  @apply bg-gray-50;
  
  .stats-card,
  .stake-card,
  .unstake-card {
    @apply shadow-lg hover:shadow-xl transition-shadow duration-300;
    @apply border-0 rounded-xl;
    
    .ant-card-head {
      @apply bg-gradient-to-r from-blue-500 to-purple-600;
      @apply border-0 rounded-t-xl;
      
      .ant-card-head-title {
        @apply text-white font-semibold;
      }
    }
  }
}
```

---

**Tiếp theo**: [05-shared-libraries.md](./05-shared-libraries.md) - Thư viện chia sẻ
