# Shared Libraries - <PERSON>h<PERSON> viện Chia sẻ

## 🎯 Tổng quan

Hệ thống sử dụng các thư viện chia sẻ để đảm bảo tính nhất quán và tái sử dụng code giữa các package. <PERSON><PERSON><PERSON> thư viện này được quản lý trong monorepo và được build riêng biệt.

## 📦 Cấu trúc Shared Libraries

### 1. Sharing Package (packages/sharing)
```
packages/sharing/
├── src/
│   ├── constants/        # Hằng số chia sẻ
│   │   ├── theme.ts     # Theme constants
│   │   ├── languages.ts # Language constants
│   │   ├── api.ts       # API endpoints
│   │   └── status.ts    # Status constants
│   ├── types/           # TypeScript types
│   │   ├── user.ts      # User types
│   │   ├── course.ts    # Course types
│   │   └── common.ts    # Common types
│   ├── utils/           # Utility functions
│   │   ├── date.ts      # Date utilities
│   │   ├── format.ts    # Format utilities
│   │   └── validation.ts # Validation utilities
│   └── index.ts         # Main export
├── dist/                # Built files
├── package.json
└── tsconfig.json
```

### 2. Token Management Package
```
packages/token-management/
├── src/
│   ├── providers/       # Token providers
│   ├── hooks/          # Token hooks
│   ├── utils/          # Token utilities
│   └── types/          # Token types
├── dist/
└── package.json
```

## 🎨 Theme Constants

### Theme Names
```typescript
// sharing/src/constants/theme.ts
export const THEME_NAMES = {
  MEDOO: "medoo",
  MEDOO_SAAS: "medoo-saas", 
  MEDOO_SAAS_V3: "medoo-saas-v3",
  MEDOO_SAAS_V4: "medoo-saas-v4",
  MEDOO_WEB3: "medoo-web3",
  MEDOO_SAAS_WEB3: "medoo-saas-web3",
  MEDOO_SAAS_WEB3_V2: "medoo-saas-web3-v2",
  MEDOO_TEST_SYSTEM: "medoo-test-system",
  MEDOO_TETHER: "medoo-tether",
  MEDOO_DIGITAL_UNIVERSITY: "medoo-digital-university",
  EDUMALL_V2: "edumall-v2",
  BRIGHT: "bright",
  RENDEMY: "rendemy",
} as const;

export type ThemeName = typeof THEME_NAMES[keyof typeof THEME_NAMES];

// Theme configuration interface
export interface ThemeConfig {
  name: ThemeName;
  displayName: string;
  description: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    text: string;
  };
  features: string[];
  layouts: string[];
}

// Theme registry
export const THEME_CONFIGS: Record<ThemeName, ThemeConfig> = {
  [THEME_NAMES.MEDOO]: {
    name: THEME_NAMES.MEDOO,
    displayName: "Medoo",
    description: "Default Medoo theme",
    colors: {
      primary: "#2F57EF",
      secondary: "#8b5cf6",
      accent: "#f59e0b",
      background: "#ffffff",
      text: "#111111",
    },
    features: ["courses", "certificates", "payments"],
    layouts: ["default", "minimal"],
  },
  [THEME_NAMES.MEDOO_WEB3]: {
    name: THEME_NAMES.MEDOO_WEB3,
    displayName: "Medoo Web3",
    description: "Web3-enabled Medoo theme",
    colors: {
      primary: "#2F57EF",
      secondary: "#8b5cf6",
      accent: "#ffd700",
      background: "#0a0a0a",
      text: "#ffffff",
    },
    features: ["courses", "nft", "tokens", "staking"],
    layouts: ["web3", "dashboard"],
  },
  // ... other themes
};
```

### Language Constants
```typescript
// sharing/src/constants/languages.ts
export const Languages = {
  EN: "en",
  VN: "vn",
} as const;

export type Language = typeof Languages[keyof typeof Languages];

export const LANGUAGE_CONFIGS = {
  [Languages.EN]: {
    code: Languages.EN,
    name: "English",
    nativeName: "English",
    flag: "🇺🇸",
    rtl: false,
  },
  [Languages.VN]: {
    code: Languages.VN,
    name: "Vietnamese",
    nativeName: "Tiếng Việt",
    flag: "🇻🇳",
    rtl: false,
  },
};

export const DEFAULT_LANGUAGE = Languages.VN;
export const SUPPORTED_LANGUAGES = Object.values(Languages);
```

## 🔧 Utility Functions

### Date Utilities
```typescript
// sharing/src/utils/date.ts
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import relativeTime from "dayjs/plugin/relativeTime";

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(relativeTime);

export class DateUtils {
  static format(date: string | Date, format = "DD/MM/YYYY"): string {
    return dayjs(date).format(format);
  }

  static formatDateTime(date: string | Date): string {
    return dayjs(date).format("DD/MM/YYYY HH:mm");
  }

  static fromNow(date: string | Date): string {
    return dayjs(date).fromNow();
  }

  static toUTC(date: string | Date): string {
    return dayjs(date).utc().toISOString();
  }

  static toTimezone(date: string | Date, timezone: string): string {
    return dayjs(date).tz(timezone).format();
  }

  static isValid(date: string | Date): boolean {
    return dayjs(date).isValid();
  }

  static addDays(date: string | Date, days: number): Date {
    return dayjs(date).add(days, "day").toDate();
  }

  static subtractDays(date: string | Date, days: number): Date {
    return dayjs(date).subtract(days, "day").toDate();
  }

  static isBefore(date1: string | Date, date2: string | Date): boolean {
    return dayjs(date1).isBefore(dayjs(date2));
  }

  static isAfter(date1: string | Date, date2: string | Date): boolean {
    return dayjs(date1).isAfter(dayjs(date2));
  }

  static getDuration(start: string | Date, end: string | Date): {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  } {
    const startDate = dayjs(start);
    const endDate = dayjs(end);
    const duration = endDate.diff(startDate);
    
    return {
      days: Math.floor(duration / (1000 * 60 * 60 * 24)),
      hours: Math.floor((duration % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
      minutes: Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((duration % (1000 * 60)) / 1000),
    };
  }
}
```

### Format Utilities
```typescript
// sharing/src/utils/format.ts
export class FormatUtils {
  // Currency formatting
  static formatCurrency(
    amount: number,
    currency = "VND",
    locale = "vi-VN"
  ): string {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency,
    }).format(amount);
  }

  // Number formatting
  static formatNumber(
    number: number,
    options: Intl.NumberFormatOptions = {}
  ): string {
    return new Intl.NumberFormat("vi-VN", options).format(number);
  }

  // File size formatting
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";
    
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // Duration formatting
  static formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${remainingSeconds
        .toString()
        .padStart(2, "0")}`;
    }
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  // Phone number formatting
  static formatPhoneNumber(phone: string): string {
    const cleaned = phone.replace(/\D/g, "");
    
    if (cleaned.startsWith("84")) {
      return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;
    }
    
    if (cleaned.startsWith("0")) {
      return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
    }
    
    return phone;
  }

  // Truncate text
  static truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + "...";
  }

  // Slug generation
  static generateSlug(text: string): string {
    return text
      .toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .replace(/[đĐ]/g, "d")
      .replace(/[^a-z0-9\s-]/g, "")
      .trim()
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-");
  }
}
```

### Validation Utilities
```typescript
// sharing/src/utils/validation.ts
export class ValidationUtils {
  // Email validation
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Phone validation (Vietnamese)
  static isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ""));
  }

  // Password strength validation
  static validatePassword(password: string): {
    isValid: boolean;
    errors: string[];
    strength: "weak" | "medium" | "strong";
  } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }
    
    if (!/\d/.test(password)) {
      errors.push("Password must contain at least one number");
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push("Password must contain at least one special character");
    }

    let strength: "weak" | "medium" | "strong" = "weak";
    
    if (errors.length === 0) {
      strength = "strong";
    } else if (errors.length <= 2) {
      strength = "medium";
    }

    return {
      isValid: errors.length === 0,
      errors,
      strength,
    };
  }

  // URL validation
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // Vietnamese ID card validation
  static isValidIdCard(idCard: string): boolean {
    const idCardRegex = /^[0-9]{9}$|^[0-9]{12}$/;
    return idCardRegex.test(idCard);
  }

  // Credit card validation (Luhn algorithm)
  static isValidCreditCard(cardNumber: string): boolean {
    const cleaned = cardNumber.replace(/\s/g, "");
    
    if (!/^\d+$/.test(cleaned)) return false;
    
    let sum = 0;
    let isEven = false;
    
    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i]);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 === 0;
  }
}
```

## 📊 TypeScript Types

### Common Types
```typescript
// sharing/src/types/common.ts
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
  meta?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: "asc" | "desc";
}

export interface FilterParams {
  search?: string;
  status?: string;
  category?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
}

export interface User extends BaseEntity {
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
  profile?: UserProfile;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  phone?: string;
  address?: string;
  dateOfBirth?: string;
  gender?: "male" | "female" | "other";
  bio?: string;
}

export enum UserRole {
  ADMIN = "admin",
  INSTRUCTOR = "instructor",
  STUDENT = "student",
  MODERATOR = "moderator",
}

export enum UserStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  SUSPENDED = "suspended",
  PENDING = "pending",
}
```

### Course Types
```typescript
// sharing/src/types/course.ts
export interface Course extends BaseEntity {
  title: string;
  description: string;
  thumbnail?: string;
  price: number;
  currency: string;
  duration: number; // in minutes
  level: CourseLevel;
  category: string;
  tags: string[];
  instructor: User;
  status: CourseStatus;
  publishedAt?: string;
  enrollmentCount: number;
  rating: number;
  reviewCount: number;
  chapters: Chapter[];
  requirements: string[];
  objectives: string[];
  metadata?: CourseMetadata;
}

export interface Chapter extends BaseEntity {
  title: string;
  description?: string;
  order: number;
  duration: number;
  lessons: Lesson[];
  isPreview: boolean;
}

export interface Lesson extends BaseEntity {
  title: string;
  description?: string;
  content: LessonContent;
  order: number;
  duration: number;
  type: LessonType;
  isPreview: boolean;
  resources: LessonResource[];
}

export interface LessonContent {
  type: "video" | "text" | "quiz" | "assignment";
  data: any;
}

export interface LessonResource {
  id: string;
  name: string;
  type: "file" | "link" | "document";
  url: string;
  size?: number;
}

export enum CourseLevel {
  BEGINNER = "beginner",
  INTERMEDIATE = "intermediate",
  ADVANCED = "advanced",
  EXPERT = "expert",
}

export enum CourseStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
  ARCHIVED = "archived",
  SUSPENDED = "suspended",
}

export enum LessonType {
  VIDEO = "video",
  TEXT = "text",
  QUIZ = "quiz",
  ASSIGNMENT = "assignment",
  LIVE = "live",
}

export interface CourseMetadata {
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  socialImage?: string;
  customFields?: Record<string, any>;
}
```

## 🔌 API Constants

### Endpoints
```typescript
// sharing/src/constants/api.ts
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: "/auth/login",
    REGISTER: "/auth/register",
    LOGOUT: "/auth/logout",
    REFRESH: "/auth/refresh",
    FORGOT_PASSWORD: "/auth/forgot-password",
    RESET_PASSWORD: "/auth/reset-password",
    VERIFY_EMAIL: "/auth/verify-email",
  },
  
  // Users
  USERS: {
    LIST: "/users",
    PROFILE: "/users/profile",
    UPDATE_PROFILE: "/users/profile",
    CHANGE_PASSWORD: "/users/change-password",
    UPLOAD_AVATAR: "/users/avatar",
  },
  
  // Courses
  COURSES: {
    LIST: "/courses",
    DETAIL: "/courses/:id",
    CREATE: "/courses",
    UPDATE: "/courses/:id",
    DELETE: "/courses/:id",
    ENROLL: "/courses/:id/enroll",
    PROGRESS: "/courses/:id/progress",
    REVIEWS: "/courses/:id/reviews",
  },
  
  // Payments
  PAYMENTS: {
    CREATE_ORDER: "/payments/orders",
    VERIFY_PAYMENT: "/payments/verify",
    PAYMENT_METHODS: "/payments/methods",
    TRANSACTION_HISTORY: "/payments/transactions",
  },
  
  // Files
  FILES: {
    UPLOAD: "/files/upload",
    DELETE: "/files/:id",
    SIGNED_URL: "/files/signed-url",
  },
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;

export const API_ERRORS = {
  NETWORK_ERROR: "NETWORK_ERROR",
  TIMEOUT_ERROR: "TIMEOUT_ERROR",
  VALIDATION_ERROR: "VALIDATION_ERROR",
  AUTHENTICATION_ERROR: "AUTHENTICATION_ERROR",
  AUTHORIZATION_ERROR: "AUTHORIZATION_ERROR",
  NOT_FOUND_ERROR: "NOT_FOUND_ERROR",
  SERVER_ERROR: "SERVER_ERROR",
} as const;
```

## 🔧 Build Configuration

### Package.json
```json
{
  "name": "sharing",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "clean": "rm -rf dist"
  },
  "dependencies": {
    "dayjs": "^1.11.11"
  },
  "devDependencies": {
    "typescript": "^5.0.4"
  }
}
```

### TypeScript Configuration
```json
{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "lib": ["es2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

---

**Tiếp theo**: [06-state-management.md](./06-state-management.md) - Quản lý State
