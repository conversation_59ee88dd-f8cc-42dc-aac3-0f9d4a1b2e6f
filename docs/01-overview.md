# Tổng quan Hệ thống Medoo Platform

## 🎯 Giới thiệu

Medoo Platform là một hệ thống giáo dục đa dạng được xây dựng theo kiến trúc monorepo, hỗ trợ nhiều theme và ứng dụng khác nhau. Hệ thống được thiết kế để phục vụ các nhu cầu giáo dục từ K-12 đến đại học, từ các khóa học truyền thống đến Web3 và blockchain.

## 🏗️ Kiến trúc Tổng quan

### Monorepo Structure
```
medoo.io/
├── packages/
│   ├── webapp/           # Ứng dụng web chính (Next.js)
│   ├── telegram-bot/     # Telegram mini-app (Vite + React)
│   ├── landing-page/     # Landing page (Vite + React)
│   ├── api-server/       # Backend API (LoopBack 4)
│   ├── worker/           # Background jobs processor
│   ├── sharing/          # Thư viện chia sẻ
│   └── proxy/            # Reverse proxy
├── lerna.json            # Lerna configuration
├── package.json          # Root package configuration
└── tsconfig.json         # TypeScript configuration
```

### Package Manager
- **Lerna**: Quản lý monorepo
- **Yarn**: Package manager chính
- **Node.js**: v18.16.0+

## 📱 Các Ứng dụng Frontend

### 1. WebApp (packages/webapp)
**Mục đích**: Ứng dụng web chính cho người dùng cuối
**Công nghệ chính**:
- Next.js 14 với App Router
- React 18
- TypeScript 5.3+
- Ant Design 5.17.2
- Redux Toolkit + Redux Saga
- SCSS + Tailwind CSS

**Tính năng chính**:
- Multi-theme support (10+ themes)
- Server-side rendering (SSR)
- Internationalization (i18n)
- Authentication & Authorization
- Course management
- Payment integration
- Web3 wallet integration

### 2. Telegram Bot (packages/telegram-bot)
**Mục đích**: Telegram mini-app cho mobile users
**Công nghệ chính**:
- Vite 5.3+
- React 18
- TypeScript 5.2+
- Ant Design Mobile 5.37+
- Hookstate (state management)
- PWA với Workbox

**Tính năng chính**:
- Telegram WebApp integration
- TON blockchain integration
- Game mechanics
- Push notifications
- Offline support

### 3. Landing Page (packages/landing-page)
**Mục đích**: Landing page cho marketing campaigns
**Công nghệ chính**:
- Vite 5.3+
- React 18
- TypeScript 5.2+
- Ant Design 5.23+
- Redux Toolkit
- Web3 integration (Wagmi + Viem)

**Tính năng chính**:
- Marketing campaigns
- Lead generation
- Web3 wallet connection
- Token staking/farming

## 🎨 Hệ thống Theme

### Theme Architecture
Hệ thống hỗ trợ đa theme động với khả năng:
- Runtime theme switching
- Theme-specific components
- Custom styling per theme
- Brand customization

### Các Theme hiện tại:
1. **Medoo** - Theme chính
2. **Medoo SaaS V4** - SaaS platform
3. **Medoo Web3** - Blockchain/Web3 focus
4. **Medoo Digital University** - Đại học số
5. **Edumall V2** - Marketplace theme
6. **Tether** - Cryptocurrency theme
7. **Bright** - Light theme variant

### Theme Configuration
```typescript
// Theme structure
interface ThemeConfig {
  name: string;
  components: {
    layout: React.ComponentType;
    pages: Record<string, React.ComponentType>;
  };
  styles: {
    colors: ColorPalette;
    typography: TypographyConfig;
    spacing: SpacingConfig;
  };
}
```

## 🔧 Công nghệ Core

### Frontend Stack
- **React 18**: UI library với concurrent features
- **TypeScript**: Type safety và developer experience
- **Next.js 14**: Full-stack React framework với App Router
- **Vite**: Fast build tool cho development

### UI/UX Stack
- **Ant Design**: Component library chính
- **Tailwind CSS**: Utility-first CSS framework
- **SCSS**: CSS preprocessor
- **Animate.css**: Animation library

### State Management
- **Redux Toolkit**: Predictable state container
- **Redux Saga**: Side effects management
- **Hookstate**: Lightweight state management
- **React Query**: Server state management

### Build & Development
- **Webpack 5**: Module bundler (Next.js)
- **SWC**: Fast TypeScript/JavaScript compiler
- **ESLint**: Code linting
- **Prettier**: Code formatting

## 🌍 Internationalization

### Supported Languages
- **Vietnamese (vn)**: Default language
- **English (en)**: Secondary language

### i18n Stack
- **i18next**: Internationalization framework
- **react-i18next**: React integration
- **next-i18next**: Next.js integration
- **i18next-browser-languagedetector**: Language detection

### Translation Structure
```
public/locales/
├── en/
│   ├── translation.json
│   ├── validation-error.json
│   └── k12.json
└── vn/
    ├── translation.json
    ├── validation-error.json
    └── k12.json
```

## 🔐 Authentication & Security

### Authentication Methods
- **JWT**: JSON Web Tokens
- **OAuth2**: Google, Facebook integration
- **Web3**: Wallet-based authentication
- **Telegram**: Telegram user authentication

### Security Features
- **CSRF Protection**: Cross-site request forgery protection
- **XSS Prevention**: Cross-site scripting prevention
- **Content Security Policy**: CSP headers
- **Rate Limiting**: API rate limiting
- **Input Validation**: Server-side validation

## 📊 Performance & Monitoring

### Performance Optimization
- **Code Splitting**: Dynamic imports
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: Webpack Bundle Analyzer
- **Caching**: Redis caching
- **CDN**: CloudFront integration

### Monitoring Tools
- **Google Analytics**: User analytics
- **Sentry**: Error tracking
- **Performance Monitoring**: Web Vitals
- **Custom Metrics**: Business metrics tracking

## 🚀 Deployment Architecture

### Environments
- **Development**: Local development
- **Test**: Testing environment
- **Staging**: Pre-production
- **Production**: Live environment

### Infrastructure
- **AWS**: Cloud infrastructure
- **Docker**: Containerization
- **PM2**: Process management
- **HAProxy**: Load balancing
- **MongoDB**: Database
- **Redis**: Caching & sessions
- **RabbitMQ**: Message queue

## 📈 Scalability Considerations

### Horizontal Scaling
- **Microservices**: Service-oriented architecture
- **Load Balancing**: Multiple instances
- **Database Sharding**: Data distribution
- **CDN**: Global content delivery

### Vertical Scaling
- **Performance Optimization**: Code optimization
- **Caching Strategies**: Multi-level caching
- **Database Optimization**: Query optimization
- **Resource Management**: Memory & CPU optimization

## 🔄 Development Workflow

### Git Workflow
- **Feature Branches**: Feature development
- **Pull Requests**: Code review process
- **Conventional Commits**: Standardized commit messages
- **Automated Testing**: CI/CD pipeline

### Code Quality
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Husky**: Git hooks
- **Lint-staged**: Pre-commit linting
- **TypeScript**: Type checking

## 📚 Documentation Structure

Tài liệu được chia thành các phần chính:
1. **Architecture**: Kiến trúc hệ thống
2. **Development**: Hướng dẫn phát triển
3. **Deployment**: Triển khai
4. **API Reference**: Tài liệu API
5. **Best Practices**: Thực hành tốt nhất

---

**Tiếp theo**: [02-webapp-architecture.md](./02-webapp-architecture.md) - Kiến trúc chi tiết của WebApp
