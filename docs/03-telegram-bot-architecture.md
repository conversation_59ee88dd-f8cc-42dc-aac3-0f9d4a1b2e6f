# Telegram Bot Architecture - Vite + React Application

## 🎯 Tổng quan

Telegram Bot là một mini-app được thiết kế để chạy trong Telegram, được xây dựng với Vite và React 18. Ứng dụng tích hợp với TON blockchain và cung cấp trải nghiệm gaming/earning cho người dùng.

## 📁 Cấu trúc Thư mục

```
packages/telegram-bot/
├── src/
│   ├── components/        # React components
│   │   ├── share/        # Shared components
│   │   ├── ton-wallet-provider/ # TON wallet integration
│   │   └── ui/           # UI components
│   ├── lib/              # Utility libraries
│   │   ├── hook/         # Custom hooks
│   │   ├── providers/    # Context providers
│   │   └── utils/        # Utility functions
│   ├── pages/            # Page components
│   ├── styles/           # Styling files
│   ├── App.tsx           # Main app component
│   ├── main.tsx          # Entry point
│   ├── i18n.ts           # Internationalization setup
│   └── index.css         # Global styles
├── public/               # Static assets
│   ├── assets/           # Images, icons
│   ├── locales/          # Translation files
│   └── manifest.json     # PWA manifest
├── vite.config.ts        # Vite configuration
├── tailwind.config.js    # Tailwind configuration
├── tsconfig.json         # TypeScript configuration
└── package.json
```

## ⚙️ Vite Configuration

### vite.config.ts
```typescript
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const isDevelopment = mode === "development";
  
  const plugins = [
    react(),
    tsconfigPaths(),
    VitePWA({
      registerType: "autoUpdate",
      injectRegister: "auto",
      includeAssets: [
        "images/*",
        "fonts/*.ttf", 
        "images/*.png",
        "images/*.webp",
        "svgs/*.svg"
      ],
      manifest: {
        name: "Medoo bot",
        short_name: "Medoo telegram bot",
        description: "Medoo telegram bot",
        theme_color: "050A23"
      },
      workbox: {
        globPatterns: ["**/*.{js,css,svg,png,ico,webp,ttf}"],
        cleanupOutdatedCaches: true,
        clientsClaim: true,
        runtimeCaching: [
          {
            urlPattern: ({ request }) => request.destination === "image",
            handler: "CacheFirst",
            options: {
              cacheName: "images-cache",
              expiration: {
                maxEntries: 50,
                maxAgeSeconds: 30 * 24 * 60 * 60, // 30 days
              },
            },
          },
        ],
      },
    }),
  ];

  // Node.js polyfills for browser
  plugins.push(nodePolyfills({
    globals: {
      Buffer: true,
      process: true
    }
  }));

  return {
    plugins,
    define: {
      global: "globalThis",
    },
    resolve: {
      alias: {
        src: path.resolve(__dirname, "./src"),
        "@": path.resolve(__dirname, "./src"),
      },
    },
    build: {
      target: "esnext",
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ["react", "react-dom"],
            antd: ["antd-mobile"],
            ton: ["@tonconnect/ui-react", "tonweb"],
          },
        },
      },
    },
  };
});
```

## 🎮 Application Entry Point

### main.tsx
```typescript
import React from "react";
import ReactDOM from "react-dom/client";
import { MemoryRouter } from "react-router-dom";
import { useGetApp } from "src/components/share/hook/init-app";
import TonWalletProvider from "src/components/ton-wallet-provider";
import { useInitGameDataHookState } from "src/lib/hook/hook";
import { AuthProvider } from "src/lib/providers/auth";
import App from "./App";
import "./index.css";
import "./i18n";

const InitApp = () => {
  useGetApp();
  useInitGameDataHookState();
  
  return (
    <React.StrictMode>
      <MemoryRouter>
        <AuthProvider>
          <TonWalletProvider>
            <App />
          </TonWalletProvider>
        </AuthProvider>
      </MemoryRouter>
    </React.StrictMode>
  );
};

ReactDOM.createRoot(document.getElementById("root")!).render(<InitApp />);
```

## 🔄 State Management với Hookstate

### Game State Management
```typescript
// lib/hook/hook.tsx
interface UserGameHookState {
  currentPoint: number;
  maxProfit: number;
  profitGame: number;
  profitGameUserGame: number;
  incrementPerEarn: number;
  progressLevel: number;
  currentLevel: DataLevel;
  gender: string;
  accessibleGroupLevel: Array<GroupType>;
  ticketQuantity: number;
  mPoint: number;
  boostMaxProfit?: number;
  boostIncrementPerEarn?: number;
  boostProfitGame?: number;
  restoreGameProfit?: {
    using: number;
    timestamp: number;
  };
  tokenBalance: any;
}

const userGameState = hookstate<UserGameHookState>({
  currentPoint: 0,
  maxProfit: 0,
  profitGame: 0,
  profitGameUserGame: 0,
  incrementPerEarn: 0,
  currentLevel: null,
  gender: "",
  progressLevel: 0,
  accessibleGroupLevel: [],
  ticketQuantity: 0,
  mPoint: 0,
  tokenBalance: {},
});

// Custom hooks for game state
export const useUserGameState = () => {
  const state = useHookstate(userGameState);
  
  const updateCurrentPoint = (newPoint: number) => {
    state.currentPoint.set(newPoint);
  };
  
  const incrementPoint = (amount: number) => {
    state.currentPoint.set(prev => prev + amount);
  };
  
  return {
    ...state.get(),
    updateCurrentPoint,
    incrementPoint,
  };
};
```

### Authentication State
```typescript
// lib/hook/authentication.ts
interface UserAuthHookStateType {
  h2token?: string;
  isWeb3User?: boolean;
  refreshToken?: string;
  expiresIn?: number;
  expiredAt?: number;
  h2user?: {
    [key: string]: any;
  };
}

export const userAuthState = hookstate<UserAuthHookStateType>({
  h2token: undefined,
  isWeb3User: false,
  refreshToken: undefined,
  expiresIn: 0,
  expiredAt: 0,
  h2user: {},
});

export const useUserAuthState = () => {
  const state = useHookstate(userAuthState);
  
  const login = (authData: UserAuthHookStateType) => {
    state.set(authData);
    Cookies.set("h2token", authData.h2token || "");
  };
  
  const logout = () => {
    state.set({
      h2token: undefined,
      isWeb3User: false,
      refreshToken: undefined,
      expiresIn: 0,
      expiredAt: 0,
      h2user: {},
    });
    Cookies.remove("h2token");
  };
  
  return {
    ...state.get(),
    login,
    logout,
  };
};
```

## 🪙 TON Blockchain Integration

### TON Wallet Provider
```typescript
// components/ton-wallet-provider/index.tsx
import { TonConnectUIProvider } from "@tonconnect/ui-react";

const TonWalletProvider = ({ children }: { children: React.ReactNode }) => {
  const manifestUrl = `${window.location.origin}/tonconnect-manifest.json`;
  
  return (
    <TonConnectUIProvider manifestUrl={manifestUrl}>
      {children}
    </TonConnectUIProvider>
  );
};

export default TonWalletProvider;
```

### TON Wallet Hooks
```typescript
// lib/hook/ton-wallet.ts
import { useTonConnectUI, useTonWallet } from "@tonconnect/ui-react";
import { Address } from "@ton/core";

export const useTonWalletConnection = () => {
  const [tonConnectUI] = useTonConnectUI();
  const wallet = useTonWallet();
  
  const connectWallet = async () => {
    try {
      await tonConnectUI.connectWallet();
    } catch (error) {
      console.error("Failed to connect wallet:", error);
    }
  };
  
  const disconnectWallet = async () => {
    try {
      await tonConnectUI.disconnect();
    } catch (error) {
      console.error("Failed to disconnect wallet:", error);
    }
  };
  
  const getWalletAddress = () => {
    if (!wallet?.account?.address) return null;
    return Address.parse(wallet.account.address).toString();
  };
  
  return {
    wallet,
    isConnected: !!wallet,
    connectWallet,
    disconnectWallet,
    walletAddress: getWalletAddress(),
  };
};
```

## 📱 UI Components với Ant Design Mobile

### Component Structure
```typescript
// components/ui/Button.tsx
import { Button as AntButton } from "antd-mobile";
import classNames from "classnames";

interface ButtonProps {
  variant?: "primary" | "secondary" | "ghost";
  size?: "small" | "medium" | "large";
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export const Button: React.FC<ButtonProps> = ({
  variant = "primary",
  size = "medium",
  loading = false,
  disabled = false,
  children,
  onClick,
  className,
}) => {
  const buttonClass = classNames(
    "custom-button",
    `custom-button--${variant}`,
    `custom-button--${size}`,
    {
      "custom-button--loading": loading,
      "custom-button--disabled": disabled,
    },
    className
  );
  
  return (
    <AntButton
      className={buttonClass}
      loading={loading}
      disabled={disabled}
      onClick={onClick}
    >
      {children}
    </AntButton>
  );
};
```

### Game UI Components
```typescript
// components/game/EarnButton.tsx
import { useState } from "react";
import { Button } from "antd-mobile";
import { useUserGameState } from "src/lib/hook/hook";

export const EarnButton = () => {
  const [isEarning, setIsEarning] = useState(false);
  const { incrementPoint, incrementPerEarn } = useUserGameState();
  
  const handleEarn = async () => {
    if (isEarning) return;
    
    setIsEarning(true);
    
    // Animate earning
    incrementPoint(incrementPerEarn);
    
    // Add visual feedback
    setTimeout(() => {
      setIsEarning(false);
    }, 300);
  };
  
  return (
    <Button
      className={`earn-button ${isEarning ? "earning" : ""}`}
      onClick={handleEarn}
      disabled={isEarning}
    >
      <div className="earn-button__content">
        <span className="earn-button__icon">💰</span>
        <span className="earn-button__text">Earn</span>
        <span className="earn-button__amount">+{incrementPerEarn}</span>
      </div>
    </Button>
  );
};
```

## 🎨 Styling với Tailwind CSS

### Tailwind Configuration
```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: "#eff6ff",
          500: "#3b82f6",
          600: "#2563eb",
          700: "#1d4ed8",
        },
        telegram: {
          bg: "#17212b",
          secondary: "#242f3d",
          text: "#ffffff",
          hint: "#708499",
          link: "#6ab7ff",
          button: "#5288c1",
        },
      },
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
      },
      animation: {
        "bounce-in": "bounceIn 0.6s ease-out",
        "slide-up": "slideUp 0.3s ease-out",
        "pulse-glow": "pulseGlow 2s infinite",
      },
    },
  },
  plugins: [],
};
```

### Component Styling
```scss
// styles/components/earn-button.scss
.earn-button {
  @apply relative overflow-hidden rounded-full bg-gradient-to-r from-blue-500 to-purple-600;
  @apply transform transition-all duration-300 ease-out;
  @apply shadow-lg hover:shadow-xl;
  
  &.earning {
    @apply scale-95;
    
    &::after {
      content: "";
      @apply absolute inset-0 bg-white opacity-20;
      animation: ripple 0.6s ease-out;
    }
  }
  
  &__content {
    @apply flex items-center justify-center space-x-2 px-6 py-3;
  }
  
  &__icon {
    @apply text-2xl;
    animation: bounce 2s infinite;
  }
  
  &__text {
    @apply font-semibold text-white;
  }
  
  &__amount {
    @apply text-sm text-yellow-300 font-bold;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}
```

## 🌐 Internationalization

### i18n Setup
```typescript
// i18n.ts
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: "en",
    debug: false,
    
    interpolation: {
      escapeValue: false,
    },
    
    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },
    
    detection: {
      order: ["localStorage", "navigator"],
      caches: ["localStorage"],
    },
  });

export default i18n;
```

### Translation Files
```json
// public/locales/en/translation.json
{
  "game": {
    "earn": "Earn",
    "points": "Points",
    "level": "Level",
    "boost": "Boost",
    "tasks": "Tasks",
    "leaderboard": "Leaderboard"
  },
  "wallet": {
    "connect": "Connect Wallet",
    "disconnect": "Disconnect",
    "balance": "Balance",
    "transaction": "Transaction"
  },
  "common": {
    "loading": "Loading...",
    "error": "Error occurred",
    "success": "Success",
    "cancel": "Cancel",
    "confirm": "Confirm"
  }
}
```

## 🔧 PWA Configuration

### Service Worker
```typescript
// vite-plugin-pwa configuration
VitePWA({
  registerType: "autoUpdate",
  injectRegister: "auto",
  workbox: {
    globPatterns: ["**/*.{js,css,svg,png,ico,webp,ttf}"],
    cleanupOutdatedCaches: true,
    clientsClaim: true,
    runtimeCaching: [
      {
        urlPattern: ({ request }) => request.destination === "image",
        handler: "CacheFirst",
        options: {
          cacheName: "images-cache",
          expiration: {
            maxEntries: 50,
            maxAgeSeconds: 30 * 24 * 60 * 60,
          },
        },
      },
      {
        urlPattern: ({ url }) => url.pathname.startsWith("/api/"),
        handler: "NetworkFirst",
        options: {
          cacheName: "api-cache",
          expiration: {
            maxEntries: 100,
            maxAgeSeconds: 5 * 60, // 5 minutes
          },
        },
      },
    ],
  },
})
```

### Manifest Configuration
```json
// public/manifest.json
{
  "name": "Medoo Bot",
  "short_name": "Medoo",
  "description": "Medoo Telegram Bot - Earn, Learn, Play",
  "theme_color": "#050A23",
  "background_color": "#050A23",
  "display": "standalone",
  "orientation": "portrait",
  "scope": "/",
  "start_url": "/",
  "icons": [
    {
      "src": "/images/icon-192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/images/icon-512.png", 
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

---

**Tiếp theo**: [04-landing-page-architecture.md](./04-landing-page-architecture.md) - Kiến trúc Landing Page
